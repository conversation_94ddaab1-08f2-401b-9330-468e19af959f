import request from '@/utils/request'

// 查询推送日志列表
export function listLog(query) {
  return request({
    url: '/android/log/list',
    method: 'get',
    params: query
  })
}

// 查询推送日志详细
export function getLog(id) {
  return request({
    url: '/android/log/' + id,
    method: 'get'
  })
}

// 新增推送日志
export function addLog(data) {
  return request({
    url: '/android/log',
    method: 'post',
    data: data
  })
}

// 修改推送日志
export function updateLog(data) {
  return request({
    url: '/android/log',
    method: 'put',
    data: data
  })
}

// 删除推送日志
export function delLog(id) {
  return request({
    url: '/android/log/' + id,
    method: 'delete'
  })
}
