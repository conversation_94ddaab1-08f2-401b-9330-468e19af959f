import request from '@/utils/request'

// 查询推送日志列表
export function listLog(query) {
  return request({
    url: '/android/log/list',
    method: 'get',
    params: query
  })
}

// 查询推送日志详细
export function getLog(id) {
  return request({
    url: '/android/log/' + id,
    method: 'get'
  })
}

// 新增推送日志
export function addPushLog(data) {
  return request({
    url: '/android/log',
    method: 'post',
    data: data
  })
}

// 修改推送日志
export function updateLog(data) {
  return request({
    url: '/android/log',
    method: 'put',
    data: data
  })
}

// 删除推送日志
export function delLog(id) {
  return request({
    url: '/android/log/' + id,
    method: 'delete'
  })
}

// 根据推送id查询设备：/android/log/getDeviceByPushLogId/{pushLogId}
export function getDeviceByPushLogId(pushLogId) {
  return request({
    url: '/android/log/getDeviceByPushLogId/' + pushLogId,
    method: 'get'
  })
}
// 根据推送id查询：/android/log/apkgetPushApkByPushLogId/{pushLogId}
export function getPushApkByPushLogId(pushLogId) {
  return request({
    url: '/android/log/getPushApkByPushLogId/' + pushLogId,
    method: 'get'
  })
}
export function getPushFileByPushLogId(pushLogId) {
  return request({
    url: '/android/log/getPushFileByPushLogId/' + pushLogId,
    method: 'get'
  })
}
// getRemainingDeviceAndApk/{pushLogId}
export function getRemainingDeviceAndApk(pushLogId) {
  return request({
    url: '/android/log/getRemainingDeviceAndApk/' + pushLogId,
    method: 'get'
  })
}
