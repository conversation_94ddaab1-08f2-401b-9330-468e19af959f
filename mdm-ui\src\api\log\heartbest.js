import request from '@/utils/request'

// 查询心跳日志列表
export function listLog(query) {
  return request({
    url: '/log/log/list',
    method: 'get',
    params: query
  })
}

// 查询心跳日志详细
export function getLog(id) {
  return request({
    url: '/log/log/' + id,
    method: 'get'
  })
}

// 新增心跳日志
export function addLog(data) {
  return request({
    url: '/log/log',
    method: 'post',
    data: data
  })
}

// 修改心跳日志
export function updateLog(data) {
  return request({
    url: '/log/log',
    method: 'put',
    data: data
  })
}

// 删除心跳日志
export function delLog(id) {
  return request({
    url: '/log/log/' + id,
    method: 'delete'
  })
}
