<template>
    <el-link type="primary" @click="handleOpen">{{ row.num }}</el-link>
    <el-dialog v-model="open" :title="'所有安装：'+'文件名--'+row.apkName+ ' ，版本号--' + row.version+ ' 的设备'" width="80%"
        :style="{ height: 'calc(100vh - 100px)' }" @close="open = false" append-to-body>
        <div class="remote-content">
            <el-table :data="data" border size="small" style="width: 100%" class="table-hgight">
                <el-table-column type="index" width="55" align="center" fixed="left" label="序号" />
                <el-table-column prop="groupName" label="分组" :width="120" show-overflow-tooltip align="center"
                    fixed="left" />
                <el-table-column prop="deviceName" label="设备名称" :width="120" show-overflow-tooltip align="center" />
                <el-table-column prop="sn" label="序列号" :width="140" show-overflow-tooltip align="center" />
                <el-table-column prop="mac" label="MAC地址" :width="140" show-overflow-tooltip align="center" />
                <el-table-column prop="ip" label="IP地址" :width="120" show-overflow-tooltip align="center" />
                <el-table-column prop="model" label="型号" :width="120" show-overflow-tooltip align="center" />
                <el-table-column prop="platform" label="平台" :width="100" show-overflow-tooltip align="center" />
                <el-table-column prop="version" label="系统版本" :width="100" show-overflow-tooltip align="center" />
                <el-table-column prop="status" label="状态" :width="80" align="center">
                    <template #default="{ row }">
                        <el-tag :type="row.status === 1 ? 'success' : 'info'">
                            {{ row.status === 1 ? '在线' : '离线' }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="rssi" label="信号强度" :width="100" show-overflow-tooltip align="center" />
                <el-table-column prop="wifiName" label="WiFi名称" :width="140" show-overflow-tooltip align="center" />
                <el-table-column prop="battery" label="电量(%)" :width="100" show-overflow-tooltip align="center" />
                <el-table-column prop="apMac" label="AP MAC" :width="140" show-overflow-tooltip align="center" />
                <el-table-column prop="isRecharge" label="是否充电" :width="100" align="center">
                    <template #default="{ row }">
                        {{ row.isRecharge === "true" ? "是" : "否" }}
                    </template>
                </el-table-column>

                <el-table-column prop="heartbeatTime" label="心跳时间" :width="170" show-overflow-tooltip align="center" />
                <el-table-column prop="createTime" label="下载时间" :width="170" show-overflow-tooltip align="center"
                    fixed="right" />
            </el-table>
        </div>
    </el-dialog>
</template>
<script setup>
import { ref } from 'vue';
import { defineProps } from 'vue';
import { getApkDownloadDevice } from '@/api/apk.js';
const props = defineProps({
    row: {
        type: Object,
        default: () => ({})
    }
});
const open = ref(false);
const data = ref([]);
const handleOpen = () => {
    open.value = true;
    if (data.value.length === 0) {
        getApkDownloadDevice(props.row).then(res => {
            data.value = res;
        }).catch(err => {
            console.error('获取APK下载设备失败:', err);
        });
    }
};
</script>
<style lang="scss" scoped>
:deep(.table-hgight) {
    height: calc(100vh - 200px);
}
</style>