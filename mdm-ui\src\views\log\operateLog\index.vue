<template>
  <div class="lf-content">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="80px">
      <el-form-item label="操作类型" prop="type">
        <el-select v-model="queryParams.type" placeholder="请选择操作类型" clearable @change="handleQuery" style="width: 200px;">
          <el-option label="控制指令" value="控制指令" />
          <el-option label="安装指令" value="安装指令" />
          <el-option label="更新指令" value="更新指令" />
          <el-option label="卸载指令" value="卸载指令" />
          <el-option label="重启指令" value="重启指令" />
          <el-option label="关机指令" value="关机指令" />
          <el-option label="get指令" value="get指令" />
        </el-select>
      </el-form-item>
      <el-form-item label="日志细分" prop="subdivisionType">
        <el-select
          v-model="queryParams.subdivisionType"
          placeholder="请选择日志细分"
          clearable
          @change="handleQuery"
          style="width: 200px;"
        >
          <el-option label="发出声音" value="发出声音" />
          <el-option label="关闭声音" value="关闭声音" />
          <el-option label="打开屏幕" value="打开屏幕" />
          <el-option label="关闭屏幕" value="关闭屏幕" />
          <el-option label="截图" value="截图" />
          <el-option label="远程" value="远程" />
          <el-option label="重启" value="重启" />
          <el-option label="音量加" value="音量加" />
          <el-option label="音量减" value="音量减" />
          <el-option label="静音" value="静音" />
          <el-option label="禁用usb" value="禁用usb" />
          <el-option label="启用usb" value="启用usb" />
          <el-option label="禁用蓝牙" value="禁用蓝牙" />
          <el-option label="启用蓝牙" value="启用蓝牙" />
          <el-option label="禁用摄像头" value="禁用摄像头" />
          <el-option label="启用摄像头" value="启用摄像头" />
          <el-option label="发送消息" value="发送消息" />
          <el-option label="获取位置" value="获取位置" />
        </el-select>
      </el-form-item>
      <el-form-item label="是否成功" prop="isSuccess">
        <el-select v-model="queryParams.isSuccess" placeholder="请选择是否成功" clearable @change="handleQuery" style="width: 200px;">
          <el-option label="成功" value="成功" />
          <el-option label="失败" value="失败" />
          <el-option label="未知" value="未知" />
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <!-- <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['operation:log:add']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate"
          v-hasPermi="['operation:log:edit']">修改</el-button>
      </el-col> -->
      <el-col :span="1.5">
        <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete"
          v-hasPermi="['operation:log:remove']">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="Download" @click="handleExport"
          v-hasPermi="['operation:log:export']">导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="logList"
      @selection-change="handleSelectionChange"
      :size="size"
      border
      class="lf-table-content-height"
      :row-class-name="tableRowClassName"
      
    >
      <!-- <el-table-column type="selection" width="55" align="center" /> -->
      <el-table-column type="index" label="序号" width="50" align="center" show-overflow-tooltip />
      <!-- <el-table-column label="主键" align="center" prop="id" width="120" show-overflow-tooltip /> -->
      <el-table-column label="操作类型" align="center" prop="type" width="120" show-overflow-tooltip sortable />
      <el-table-column label="日志细分" align="center" prop="subdivisionType" width="120" show-overflow-tooltip />
      <el-table-column label="是否成功" align="center" prop="isSuccess" width="110" show-overflow-tooltip sortable >
      </el-table-column>
      <el-table-column label="MAC" align="center" prop="mac" width="150" show-overflow-tooltip />
      <el-table-column label="分组" align="center" prop="groupName" width="100" show-overflow-tooltip sortable />
      <el-table-column label="IP" align="center" prop="ip" width="130" show-overflow-tooltip sortable />
      <el-table-column label="设备名称" align="center" prop="deviceName" width="150" show-overflow-tooltip />
      <el-table-column label="操作时间" align="center" prop="createTime" width="180" show-overflow-tooltip sortable />
      <!-- <el-table-column label="设备id" align="center" prop="deviceId" width="120" show-overflow-tooltip /> -->
      <el-table-column label="日志内容" align="center" prop="content" show-overflow-tooltip width="200"/>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="150" fixed="right" show-overflow-tooltip>
        <template #default="scope">
          <!-- <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)"
            v-hasPermi="['operation:log:edit']">修改</el-button> -->
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)"
            v-hasPermi="['operation:log:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize" @pagination="getList" />

    <!-- 添加或修改操作日志对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="logRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="操作类型" prop="type">
          <el-input v-model="form.type" placeholder="请输入操作类型" />
        </el-form-item>
        <el-form-item label="日志细分" prop="subdivisionType">
          <el-input v-model="form.subdivisionType" placeholder="请输入日志细分" />
        </el-form-item>
        <el-form-item label="是否成功" prop="isSuccess">
          <el-input v-model="form.isSuccess" placeholder="请输入是否成功" />
        </el-form-item>
        <el-form-item label="设备id" prop="deviceId">
          <el-input v-model="form.deviceId" placeholder="请输入设备id" />
        </el-form-item>
        <el-form-item label="日志内容" prop="content">
          <el-input v-model="form.content" type="textarea" placeholder="请输入日志内容" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Log">
import { listLog, getLog, delLog, addLog, updateLog } from "@/api/log/operate.js";

const { proxy } = getCurrentInstance();

const logList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 20,
    type: null,
    subdivisionType: null,
    isSuccess: null,
    deviceId: null,
    content: null,
  },
  rules: {
  }
});

const { queryParams, form, rules } = toRefs(data);
const size = ref("small");

/** 查询操作日志列表 */
function getList() {
  loading.value = true;
  listLog(queryParams.value).then(response => {
    logList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

// 根据isSuccess设置行样式
function tableRowClassName({ row }) {
  if (row.isSuccess === '成功' || row.isSuccess === 1) {
    return 'success-row';
  } else if (row.isSuccess === '失败' || row.isSuccess === 0) {
    return 'warning-row';
  } else if (row.isSuccess === '未知') {
    return '';
  }
  return '';
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    type: null,
    subdivisionType: null,
    isSuccess: null,
    deviceId: null,
    content: null,
    createTime: null
  };
  proxy.resetForm("logRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加操作日志";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _id = row.id || ids.value
  getLog(_id).then(response => {
    form.value = response.data;
    open.value = true;
    title.value = "修改操作日志";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["logRef"].validate(valid => {
    if (valid) {
      if (form.value.id != null) {
        updateLog(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addLog(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value;
  proxy.$modal.confirm('是否确认删除操作日志编号为"' + _ids + '"的数据项？').then(function () {
    return delLog(_ids);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => { });
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('operation/log/export', {
    ...queryParams.value
  }, `log_${new Date().getTime()}.xlsx`)
}

getList();
</script>
<style lang="scss" scoped>
.lf-table-content-height {
  height: calc(100vh - 250px) !important;
}

:deep(.el-table__inner-wrapper:before) {
  height: 0px;
  content: " ";
  display: block;
}

:deep(.el-form-item) {
    margin-bottom: 10px !important;
}

:deep(.el-table .warning-row) {
  --el-table-tr-bg-color: #fef0f0;
}
:deep(.el-table .success-row) {
  --el-table-tr-bg-color: var(--el-color-success-light-9);
}
</style>
