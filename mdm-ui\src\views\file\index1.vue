<template>
    <div class="lf-content file">
        <el-row>
            <el-col :span="1.5">
                <el-button type="success" text bg @click="handleImport">上传文件</el-button>
            </el-col>
        </el-row>
        <div class="file-content">
            <el-radio-group v-model="fileListType" size="small">
                <el-radio-button label="按上传时间" value="按上传时间" />
                <el-radio-button label="按文件类型" value="按文件类型" />
            </el-radio-group>
            <div class="file-list" v-if="fileListType === '按上传时间'">
                <el-timeline style="max-width: 840px">
                    <el-timeline-item :timestamp="item.timestamp" placement="top" v-for="item in fileTimeList">
                        <el-card>
                            <div class="item" v-for="fileItem in item.fileList"
                                @contextmenu.prevent="handleRightClick(item, fileItem)">
                                <svg aria-hidden="true" class="svg-icon" style="font-size: 48px; padding-bottom: 4px">
                                    <use :xlink:href="`#icon-${fileItem.split('.').pop()}`"></use>
                                </svg>
                                <el-tooltip class="box-item" effect="light" :content="fileItem"><el-text class="mx-1"
                                        truncated>{{ fileItem }}</el-text></el-tooltip>
                            </div>
                        </el-card>
                    </el-timeline-item>
                </el-timeline>
            </div>
            <div class="file-list" v-else>
                <el-tabs type="border-card" class="demo-tabs">
                    <el-tab-pane :label="item.type" v-for="item in fileTypeList">
                        <div class="item" v-for="fileItem in item.fileList"
                            @contextmenu.prevent="handleRightClick(item, fileItem)">
                            <svg aria-hidden="true" class="svg-icon" style="font-size: 48px; padding-bottom: 4px">
                                <use :xlink:href="`#icon-${fileItem.fileName.split('.').pop()}`"></use>
                            </svg>
                            <el-tooltip class="box-item" effect="light" :content="fileItem"><el-text class="mx-1"
                                    truncated>{{ fileItem.fileName }}</el-text></el-tooltip>
                        </div>
                    </el-tab-pane>
                </el-tabs>
            </div>
        </div>
        <!-- 用户导入对话框 -->
        <el-dialog :title="upload.title" v-model="upload.open" width="400px" append-to-body>
            <el-upload ref="uploadRef" :limit="1" accept=".apk,.doc,.zip,.jpg,.pdf,.png,.xls,.zip,.txt,.xml"
                :headers="upload.headers" :action="upload.url" :disabled="upload.isUploading"
                :on-progress="handleFileUploadProgress" :on-success="handleFileSuccess" :auto-upload="false" drag>
                <el-icon class="el-icon--upload"><upload-filled /></el-icon>
                <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em>
                    <div class="el-upload__tip">只能上传apk/doc/zip/jpg/pdf/png/xls/zip/txt/xml文件，且不超过100MB</div>
                </div>

            </el-upload>
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" @click="submitFileForm">确 定</el-button>
                    <el-button @click="upload.open = false">取 消</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>
<script setup>
import { ElMessageBox, ElMessage } from "element-plus";
import { getToken } from "@/utils/auth";
import { getApkList, deleteItem } from "@/api/apk.js";
const { proxy } = getCurrentInstance();
const fileListType = ref('按上传时间');
const fileTimeList = ref([]);
const fileTypeList = ref([]);
const getList = () => {
    getApkList().then(res => {
        console.log(res);
        fileTimeList.value = res.length > 0 ? res.reduce((prev, cur) => {
            if (cur.monthList.length > 0) {
                for (let i = 0; i < cur.monthList.length; i++) {
                    prev.push({
                        timestamp: cur.year + "/" + cur.monthList[i].month,
                        fileList: cur.monthList[i].fileList
                    })
                }
            }
            return prev;
        }, []) : [];
        console.log(fileTimeList.value);
        handleFileTypeList(res);
    });
};

// 处理按文件类型的数据
function handleFileTypeList(res) {
    fileTypeList.value = res.length > 0 ? res.reduce((prev, cur) => {
        if (cur.monthList.length > 0) {
            for (let i = 0; i < cur.monthList.length; i++) {
                for (let j = 0; j < cur.monthList[i].fileList.length; j++) {
                    const currentFile = cur.monthList[i].fileList[j];
                    const fileType = currentFile.split('.').pop();
                    const existing = prev.find(item => item.type === fileType);
                    if (existing) {
                        existing.fileList.push({
                            fileName: currentFile,
                            year: cur.year,
                            month: cur.monthList[i].month
                        });
                    } else {
                        prev.push({
                            type: fileType,
                            fileList: [{
                                fileName: currentFile,
                                year: cur.year,
                                month: cur.monthList[i].month
                            }]
                        });
                    }
                }
            }
        }
        return prev;
    }, []) : [];
    console.log(fileTypeList.value);
}
function handleRightClick(item, fileItem) {
    console.log(item, fileItem);
    ElMessageBox.confirm(
        `确定要删除 ${fileItem} 吗？`,
        "提示",
        {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
        }
    )
        .then(() => {
            const [year, month] = item.timestamp.split('/');
            const data = { fileName: fileItem, month: month, year: year }
            // return
            deleteItem(data).then(res => {
                // 这里执行删除操作
                ElMessage.success("删除成功");
                getList();
            }, e => {
                ElMessage.error(e.message)
            })
        })
        .catch(() => {
            ElMessage.info("取消删除");
        });
}
/*** 用户上传参数 */
const upload = reactive({
    // 是否显示弹出层（用户导入）
    open: false,
    // 弹出层标题（用户导入）
    title: "",
    // 是否禁用上传
    isUploading: false,
    // 是否更新已经存在的用户数据
    updateSupport: 0,
    // 设置上传的请求头部
    headers: { Authorization: "Bearer " + getToken() },
    // 上传的地址
    url: import.meta.env.VITE_APP_BASE_API + "/android/push/apk"
});
/** 导入按钮操作 */
function handleImport() {
    upload.title = "导入APK";
    upload.open = true;
};
/**文件上传中处理 */
const handleFileUploadProgress = (event, file, fileList) => {
    upload.isUploading = true;
};

/** 文件上传成功处理 */
const handleFileSuccess = (response, file, fileList) => {
    upload.open = false;
    upload.isUploading = false;
    proxy.$refs["uploadRef"].handleRemove(file);
    proxy.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + "</div>", "上传结果", { dangerouslyUseHTMLString: true });
    getList();
};
/** 提交上传文件 */
function submitFileForm() {
    proxy.$refs["uploadRef"].submit();
};
getList();
</script>
<style lang="scss" scoped>
.file {
    .file-content {
        width: 100%;
        height: calc(100vh - 170px);
        border: 1px solid #ccc;
        margin-top: 10px;
        padding: 5px;

        .file-list {
            width: 100%;
            height: calc(100% - 30px);
            overflow: auto;
            padding-top: 10px;

            .item {
                float: left;
                margin-right: 10px;
                margin-bottom: 10px;
                width: 80px;
                height: 100px;
                border-radius: 10px;
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                transition: all 0.2s linear;

            }

            .item:hover {
                box-shadow: 0 2px 18px 0 rgba(0, 0, 0, 0.08);
                border-radius: 8px;
            }

            .type-list {
                width: 100%;
                height: 100%;
                background-color: aqua;
            }
        }
    }
}

.demo-tabs .custom-tabs-label .el-icon {
    vertical-align: middle;
}

.demo-tabs .custom-tabs-label span {
    vertical-align: middle;
    margin-left: 4px;
}

.el-tabs--border-card {
    height: 100%;
    overflow: hidden;
}

:deep(.el-tabs__content) {
    width: 100%;
    height: calc(100% - 40px);
    overflow: hidden;
}

// 输入框的margin-bottom
:deep(.el-form-item) {
    margin-bottom: 8px !important;
}
</style>