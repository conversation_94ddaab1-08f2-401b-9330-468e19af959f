{"name": "ruoyi", "version": "3.8.8", "description": "MDM管理系统", "author": "若依", "license": "MIT", "type": "module", "scripts": {"dev": "vite", "build:prod": "vite build", "build:stage": "vite build --mode staging", "preview": "vite preview"}, "repository": {"type": "git", "url": "https://gitee.com/y_project/RuoYi-Vue.git"}, "dependencies": {"@element-plus/icons-vue": "2.3.1", "@vueup/vue-quill": "1.2.0", "@vueuse/core": "10.11.0", "axios": "0.28.1", "echarts": "5.5.1", "echarts-stat": "^1.2.0", "element-plus": "2.7.6", "file-saver": "2.0.5", "fuse.js": "6.6.2", "js-cookie": "3.0.5", "jsencrypt": "3.3.2", "nprogress": "0.2.0", "pinia": "2.1.7", "qrcode": "^1.5.4", "rollup": "^4.41.1", "splitpanes": "4.0.4", "vue": "3.4.31", "vue-cropper": "1.1.1", "vue-router": "4.4.0"}, "devDependencies": {"@vitejs/plugin-vue": "5.0.5", "sass": "1.77.5", "unplugin-auto-import": "0.17.6", "unplugin-vue-setup-extend-plus": "1.0.1", "vite": "5.3.2", "vite-plugin-compression": "0.5.1", "vite-plugin-svg-icons": "2.0.1"}}