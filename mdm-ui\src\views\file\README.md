# 文件上传页面 (file)

## 📋 页面概述

文件上传页面用于管理和上传各种资源文件，支持APK文件和其他类型文件的上传。

## ✅ APK路径输入功能

### 🎯 功能需求

当上传的资源文件不是APK文件时，需要在上传文件的下方添加一个输入框来输入存放路径，对应的字段是`apkPath`。如果是APK文件，则隐藏输入框并清空输入框的值。

**重要：如果是非APK文件，文件存放路径是必填项。**

### 🔧 实现方案

**1. 文件类型检测**
```javascript
/** 文件选择变化处理 */
function handleFileChange(file) {
    if (file && file.name) {
        currentFileName.value = file.name;
        const fileExtension = file.name.split('.').pop().toLowerCase();
        
        // 判断是否为APK文件
        if (fileExtension === 'apk') {
            // 是APK文件，隐藏输入框并清空值
            showApkPathInput.value = false;
            apkPath.value = '';
        } else {
            // 不是APK文件，显示输入框
            showApkPathInput.value = true;
        }
    }
}
```

**2. 条件显示的必填输入框**
```vue
<!-- 存放路径输入框 - 非APK文件时显示 -->
<el-row v-if="showApkPathInput" style="margin-top: 15px;">
    <el-col>
        <el-form-item label="存放路径：" label-width="90px" required>
            <el-input
                v-model="apkPath"
                placeholder="文件存放路径 例：download/com.wavelink.velocity"
                clearable
                :class="{ 'is-error': hasPathError }">
            </el-input>
            <div v-if="hasPathError" class="el-form-item__error">
                请输入文件存放路径
            </div>
        </el-form-item>
    </el-col>
</el-row>
```

**3. 必填验证和数据处理**
```javascript
/** 提交上传文件 */
function submitFileForm() {
    // 验证非APK文件的存放路径是否必填
    if (showApkPathInput.value && (!apkPath.value || apkPath.value.trim() === '')) {
        proxy.$modal.msgWarning("请输入文件存放路径");
        return;
    }

    proxy.$refs["uploadRef"].submit();
}

/** 设置上传的文件路径 */
function setFilePath() {
    const path = uploadPath.value.join("\\");
    const data = { uploadPath: `\\${path}\\` };

    // 如果不是APK文件且有apkPath，则添加到上传数据中
    if (showApkPathInput.value && apkPath.value) {
        data.apkPath = apkPath.value;
    }

    return data;
}
```

### 📊 响应式数据

```javascript
// 存放路径相关
const apkPath = ref('');              // 存放路径值
const showApkPathInput = ref(false);  // 是否显示输入框
const currentFileName = ref('');      // 当前文件名

// 计算属性：检查路径是否有错误
const hasPathError = computed(() => {
    return showApkPathInput.value && (!apkPath.value || apkPath.value.trim() === '');
});
```

### 🔄 业务逻辑

**文件上传流程：**
1. **打开上传对话框**：重置所有状态
2. **选择文件**：检测文件扩展名
3. **条件显示**：
   - APK文件：隐藏输入框，清空apkPath值
   - 非APK文件：显示输入框，允许用户输入路径
4. **数据提交**：将apkPath包含在上传数据中

**状态管理：**
- `showApkPathInput`：控制输入框的显示/隐藏
- `apkPath`：存储用户输入的APK路径
- `currentFileName`：记录当前选择的文件名

### 🎯 用户体验

**APK文件上传：**
- 选择APK文件后，路径输入框自动隐藏
- 之前输入的路径值自动清空
- 界面简洁，无多余输入项

**非APK文件上传：**
- 选择非APK文件后，路径输入框自动显示
- **必填验证**：存放路径为必填项，不能为空
- 提供清晰的标签和占位符提示
- 支持清空按钮，方便用户修改
- **实时验证**：输入为空时显示错误提示和红色边框

### 📝 技术实现

**1. 文件类型检测**
- 通过文件扩展名判断是否为APK
- 支持大小写不敏感的检测
- 实时响应文件选择变化

**2. 条件渲染**
- 使用`v-if`指令控制输入框显示
- 响应式数据驱动界面变化
- 自动布局调整

**3. 数据传递**
- 动态构建上传数据对象
- 只在需要时包含apkPath字段
- 保持向后兼容性

### 🔧 扩展性

该实现方案具有良好的扩展性：
- 可以轻松添加其他文件类型的特殊处理
- 支持多个条件输入框
- 易于维护和修改

## 🎯 使用说明

1. **上传APK文件**：
   - 点击"上传文件"按钮
   - 选择APK文件
   - 路径输入框自动隐藏
   - 直接点击"确定"上传

2. **上传其他文件**：
   - 点击"上传文件"按钮
   - 选择非APK文件
   - 路径输入框自动显示
   - 输入APK路径
   - 点击"确定"上传
