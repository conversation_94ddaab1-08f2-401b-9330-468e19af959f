<template>
    <div class="app-container">
        <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
            <el-form-item label="推送人" prop="pushUser">
                <el-input v-model="queryParams.pushUser" placeholder="请输入推送人" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <!-- <el-form-item label="开始时间" prop="startTime">
                <el-date-picker clearable v-model="queryParams.startTime" type="date" value-format="YYYY-MM-DD"
                    placeholder="请选择开始时间">
                </el-date-picker>
            </el-form-item>
            <el-form-item label="应安装数" prop="installApkCount">
                <el-input v-model="queryParams.installApkCount" placeholder="请输入应安装数" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="实际安装数" prop="installedApkCount">
                <el-input v-model="queryParams.installedApkCount" placeholder="请输入实际安装数" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="任务完成量" prop="completionPercentage">
                <el-input v-model="queryParams.completionPercentage" placeholder="请输入任务完成量" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="结束时间" prop="endTime">
                <el-date-picker clearable v-model="queryParams.endTime" type="date" value-format="YYYY-MM-DD"
                    placeholder="请选择结束时间">
                </el-date-picker>
            </el-form-item>
            <el-form-item label="剩余apk数" prop="remainingCount">
                <el-input v-model="queryParams.remainingCount" placeholder="请输入剩余apk数" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item> -->
            <el-form-item>
                <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
                <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
            <!-- <el-col :span="1.5">
                <el-button type="primary" plain icon="Plus" @click="handleAdd"
                    v-hasPermi="['android:log:add']">新增</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate"
                    v-hasPermi="['android:log:edit']">修改</el-button>
            </el-col> -->
            <el-col :span="1.5">
                <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete"
                    v-hasPermi="['android:log:remove']">删除</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="warning" plain icon="Download" @click="handleExport"
                    v-hasPermi="['android:log:export']">导出</el-button>
            </el-col>
            <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>

        <el-table v-loading="loading" :data="logList" @selection-change="handleSelectionChange" border size="small" class="lf-table-content-height">
            <el-table-column type="selection" width="50" align="center" />
            <el-table-column type="index" label="序号" width="50" align="center" show-overflow-tooltip />
            <el-table-column label="推送人" align="center" prop="pushUser" width="100" show-overflow-tooltip />
            <el-table-column label="所选设备" align="center" width="120" show-overflow-tooltip>
                <template #default="scope">
                    <el-button link type="primary" @click="showDeviceDetails(scope.row)">
                        查看设备 ({{ scope.row.deviceIds ? scope.row.deviceIds.split(',').length : 0 }})
                    </el-button>
                </template>
            </el-table-column>
            <el-table-column label="推送APK" align="center" width="120" show-overflow-tooltip>
                <template #default="scope">
                    <el-button link type="primary" @click="showApkDetails(scope.row)">
                        查看APK ({{ scope.row.apkIds ? scope.row.apkIds.split(',').length : 0 }})
                    </el-button>
                </template>
            </el-table-column>
            <el-table-column label="开始时间" align="center" prop="startTime" width="150" show-overflow-tooltip sortable>
                <template #default="scope">
                    <span>{{ parseTime(scope.row.startTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
                </template>
            </el-table-column>
            <el-table-column label="结束时间" align="center" prop="endTime" width="150" show-overflow-tooltip sortable>
                <template #default="scope">
                    <span>{{ parseTime(scope.row.endTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
                </template>
            </el-table-column>
            <el-table-column label="应安装数" align="center" prop="installApkCount" width="100" show-overflow-tooltip />
            <el-table-column label="实际安装数" align="center" prop="installedApkCount" width="100" show-overflow-tooltip />
            <el-table-column label="任务完成量" align="center" width="100" show-overflow-tooltip>
                <template #default="scope">
                    <el-progress :percentage="parseFloat(scope.row.completionPercentage || 0)"
                        :color="getProgressColor(scope.row.completionPercentage)" />
                </template>
            </el-table-column>
            <el-table-column label="剩余APK数" align="center" prop="remainingCount" width="100" show-overflow-tooltip />
            <el-table-column label="操作" align="center" class-name="small-padding fixed-width" fixed="right" width="160">
                <template #default="scope">
                    <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)"
                        v-hasPermi="['android:log:edit']">修改</el-button>
                    <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)"
                        v-hasPermi="['android:log:remove']">删除</el-button>
                </template>
            </el-table-column>
        </el-table>

        <pagination v-show="total >= 0" :total="total" v-model:page="queryParams.pageNum"
            v-model:limit="queryParams.pageSize" @pagination="getList" />

        <!-- 添加或修改推送日志对话框 -->
        <el-dialog :title="title" v-model="open" width="500px" append-to-body>
            <el-form ref="logRef" :model="form" :rules="rules" label-width="80px">
                <el-form-item label="推送人" prop="pushUser">
                    <el-input v-model="form.pushUser" placeholder="请输入推送人" />
                </el-form-item>
                <el-form-item label="设备所有id" prop="deviceIds">
                    <el-input v-model="form.deviceIds" type="textarea" placeholder="请输入内容" />
                </el-form-item>
                <el-form-item label="所有apkid" prop="apkIds">
                    <el-input v-model="form.apkIds" type="textarea" placeholder="请输入内容" />
                </el-form-item>
                <el-form-item label="开始时间" prop="startTime">
                    <el-date-picker clearable v-model="form.startTime" type="date" value-format="YYYY-MM-DD"
                        placeholder="请选择开始时间">
                    </el-date-picker>
                </el-form-item>
                <el-form-item label="应安装数" prop="installApkCount">
                    <el-input v-model="form.installApkCount" placeholder="请输入应安装数" />
                </el-form-item>
                <el-form-item label="实际安装数" prop="installedApkCount">
                    <el-input v-model="form.installedApkCount" placeholder="请输入实际安装数" />
                </el-form-item>
                <el-form-item label="任务完成量" prop="completionPercentage">
                    <el-input v-model="form.completionPercentage" placeholder="请输入任务完成量" />
                </el-form-item>
                <el-form-item label="结束时间" prop="endTime">
                    <el-date-picker clearable v-model="form.endTime" type="date" value-format="YYYY-MM-DD"
                        placeholder="请选择结束时间">
                    </el-date-picker>
                </el-form-item>
                <el-form-item label="剩余apk数" prop="remainingCount">
                    <el-input v-model="form.remainingCount" placeholder="请输入剩余apk数" />
                </el-form-item>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" @click="submitForm">确 定</el-button>
                    <el-button @click="cancel">取 消</el-button>
                </div>
            </template>
        </el-dialog>

        <!-- 设备详情对话框 -->
        <el-dialog v-model="deviceDetailOpen" title="设备详情" width="800px" style="height: 600px;" append-to-body>
            <el-table :data="deviceDetailList" border class="deviceDetail_height">
                <el-table-column type="index" label="序号" width="60" align="center" />
                <el-table-column label="设备型号" prop="model" show-overflow-tooltip />
                <el-table-column label="设备SN" prop="sn" show-overflow-tooltip />
                <el-table-column label="IP地址" prop="ip" show-overflow-tooltip />
                <el-table-column label="MAC地址" prop="mac" show-overflow-tooltip />
                <el-table-column label="所属组" prop="groupName" show-overflow-tooltip />
            </el-table>
        </el-dialog>

        <!-- APK详情对话框 -->
        <el-dialog v-model="apkDetailOpen" title="APK详情" width="800px" style="height: 600px;" append-to-body>
            <el-table :data="apkDetailList" size="small" border class="apkDetail_height">
                <el-table-column type="index" label="序号" width="60" align="center" />
                <el-table-column label="APK图标" width="80" align="center">
                    <template #default="scope">
                        <img :src="`http://${scope.row.apkImg || scope.row.apkImage}`" alt="APK图标"
                            style="width: 28px; height: 28px; border-radius: 4px;" @error="handleImageError" />
                    </template>
                </el-table-column>
                <el-table-column label="APK名称" prop="apkName" show-overflow-tooltip />
                <el-table-column label="包名" prop="apkPackage" show-overflow-tooltip />
                <el-table-column label="版本" prop="version" show-overflow-tooltip />
            </el-table>
        </el-dialog>
    </div>
</template>

<script setup name="Log">
import { listLog, getLog, delLog, addPushLog, updateLog } from "@/api/pushLog.js";

const { proxy } = getCurrentInstance();

const logList = ref([]);
const open = ref(false);
const loading = ref(false);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

// 详情对话框相关
const deviceDetailOpen = ref(false);
const apkDetailOpen = ref(false);
const deviceDetailList = ref([]);
const apkDetailList = ref([]);

const data = reactive({
    form: {},
    queryParams: {
        pageNum: 1,
        pageSize: 20,
        pushUser: null,
        deviceIds: null,
        apkIds: null,
        startTime: null,
        installApkCount: null,
        installedApkCount: null,
        completionPercentage: null,
        endTime: null,
        remainingCount: null
    },
    rules: {
    }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询推送日志列表 */
function getList() {
    // loading.value = true;
    listLog(queryParams.value).then(response => {
        logList.value = response.rows;
        total.value = response.total;
        loading.value = false;
    });
}

// 取消按钮
function cancel() {
    open.value = false;
    reset();
}

// 表单重置
function reset() {
    form.value = {
        id: null,
        pushUser: null,
        deviceIds: null,
        apkIds: null,
        startTime: null,
        installApkCount: null,
        installedApkCount: null,
        completionPercentage: null,
        endTime: null,
        remainingCount: null
    };
    proxy.resetForm("logRef");
}

/** 搜索按钮操作 */
function handleQuery() {
    queryParams.value.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
    ids.value = selection.map(item => item.id);
    single.value = selection.length != 1;
    multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
    reset();
    open.value = true;
    title.value = "添加推送日志";
}

/** 修改按钮操作 */
function handleUpdate(row) {
    reset();
    const _id = row.id || ids.value
    getLog(_id).then(response => {
        form.value = response.data;
        open.value = true;
        title.value = "修改推送日志";
    });
}

/** 提交按钮 */
function submitForm() {
    proxy.$refs["logRef"].validate(valid => {
        if (valid) {
            if (form.value.id != null) {
                updateLog(form.value).then(() => {
                    proxy.$modal.msgSuccess("修改成功");
                    open.value = false;
                    getList();
                });
            } else {
                addPushLog(form.value).then(() => {
                    proxy.$modal.msgSuccess("新增成功");
                    open.value = false;
                    getList();
                });
            }
        }
    });
}

/** 删除按钮操作 */
function handleDelete(row) {
    const _ids = row.id || ids.value;
    proxy.$modal.confirm('是否确认删除推送日志编号为"' + _ids + '"的数据项？').then(function () {
        return delLog(_ids);
    }).then(() => {
        getList();
        proxy.$modal.msgSuccess("删除成功");
    }).catch(() => { });
}

/** 导出按钮操作 */
function handleExport() {
    proxy.download('android/log/export', {
        ...queryParams.value
    }, `log_${new Date().getTime()}.xlsx`)
}

/** 获取进度条颜色 */
function getProgressColor(percentage) {
    const percent = parseFloat(percentage || 0);
    if (percent >= 90) return '#67c23a';
    if (percent >= 70) return '#e6a23c';
    if (percent >= 50) return '#f56c6c';
    return '#909399';
}

/** 显示设备详情 */
function showDeviceDetails(row) {
    if (!row.deviceIds) {
        proxy.$modal.msgWarning("该日志没有设备信息");
        return;
    }

    // 模拟设备详情数据，实际应该调用API获取
    const deviceIds = row.deviceIds.split(',');
    deviceDetailList.value = deviceIds.map((id, index) => ({
        id: id,
        model: `设备型号${index + 1}`,
        sn: `SN${id}`,
        ip: `192.168.1.${100 + index}`,
        mac: `AA:BB:CC:DD:EE:${(index + 10).toString(16).toUpperCase()}`,
        groupName: `组${index + 1}`
    }));

    deviceDetailOpen.value = true;
}

/** 显示APK详情 */
function showApkDetails(row) {
    if (!row.apkIds) {
        proxy.$modal.msgWarning("该日志没有APK信息");
        return;
    }

    // 模拟APK详情数据，实际应该调用API获取
    const apkIds = row.apkIds.split(',');
    apkDetailList.value = apkIds.map((id, index) => ({
        id: id,
        apkName: `应用${index + 1}.apk`,
        apkPackage: `com.example.app${index + 1}`,
        version: `1.${index + 1}.0`,
        apkImg: '************:5006/profile/upload/default.jpg'
    }));

    apkDetailOpen.value = true;
}

/** 图片加载错误处理 */
function handleImageError(event) {
    event.target.style.display = 'none';
}

getList();
</script>

<style lang="scss" scoped>
.lf-table-content-height {
    height: calc(100vh - 260px) !important;
}
.deviceDetail_height {
    height: calc(800px - 270px) !important;
}

.apkDetail_height {
    height: calc(800px - 270px) !important;
}

:deep(.el-table__inner-wrapper:before) {
    height: 0.8px !important;
}

:deep(.el-table__inner-wrapper) {
    border-radius: 8px;
}

:deep(.el-table) {
    border-radius: 4px;
}

:deep(.el-table th.el-table__cell) {
    background-color: #f5f7fa;
    color: #606266;
    font-weight: 600;
}

:deep(.el-table .el-table__row:hover > td) {
    background-color: #f5f7fa;
}

:deep(.el-progress-bar__outer) {
    border-radius: 10px;
}

:deep(.el-progress-bar__inner) {
    border-radius: 10px;
}
</style>
