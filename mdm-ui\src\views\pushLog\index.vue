<template>
    <div class="app-container">
        <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
            <el-form-item label="推送人" prop="nickName">
                <el-input v-model="queryParams.nickName" placeholder="请输入推送人" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <!-- <el-form-item label="开始时间" prop="startTime">
                <el-date-picker clearable v-model="queryParams.startTime" type="date" value-format="YYYY-MM-DD"
                    placeholder="请选择开始时间">
                </el-date-picker>
            </el-form-item>
            <el-form-item label="应安装数" prop="installApkCount">
                <el-input v-model="queryParams.installApkCount" placeholder="请输入应安装数" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="实际安装数" prop="installedApkCount">
                <el-input v-model="queryParams.installedApkCount" placeholder="请输入实际安装数" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="任务完成度" prop="completionPercentage">
                <el-input v-model="queryParams.completionPercentage" placeholder="请输入任务完成度" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="结束时间" prop="endTime">
                <el-date-picker clearable v-model="queryParams.endTime" type="date" value-format="YYYY-MM-DD"
                    placeholder="请选择结束时间">
                </el-date-picker>
            </el-form-item>
            <el-form-item label="剩余apk数" prop="remainingCount">
                <el-input v-model="queryParams.remainingCount" placeholder="请输入剩余apk数" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item> -->
            <el-form-item>
                <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
                <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
            <!-- <el-col :span="1.5">
                <el-button type="primary" plain icon="Plus" @click="handleAdd"
                    v-hasPermi="['android:log:add']">新增</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate"
                    v-hasPermi="['android:log:edit']">修改</el-button>
            </el-col> -->
            <el-col :span="1.5">
                <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete"
                    v-hasPermi="['android:log:remove']">删除</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="warning" plain icon="Download" @click="handleExport"
                    v-hasPermi="['android:log:export']">导出</el-button>
            </el-col>
            <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>

        <el-table v-loading="loading" :data="logList" @selection-change="handleSelectionChange" border size="small" class="lf-table-content-height" stripe>
            <el-table-column type="selection" width="50" align="center" />
            <el-table-column type="index" label="序号" width="50" align="center" show-overflow-tooltip />
            <el-table-column label="剩余APK数" align="center" width="100" show-overflow-tooltip>
                <template #default="scope">
                    <el-button link type="primary" @click="showRemainingDevices(scope.row)" size="small">
                        {{ scope.row.remainingCount || 0 }}
                    </el-button>
                </template>
            </el-table-column>
            <el-table-column label="推送人" align="center" prop="nickName" width="100" show-overflow-tooltip />
            <el-table-column label="所选设备" align="center" width="120" show-overflow-tooltip>
                <template #default="scope">
                    <el-button link type="primary" @click="showDeviceDetails(scope.row)" size="small">
                        查看设备 ({{ scope.row.deviceIds ? scope.row.deviceIds.split(',').length : 0 }})
                    </el-button>
                </template>
            </el-table-column>
            <el-table-column label="推送APK" align="center" width="120" show-overflow-tooltip>
                <template #default="scope">
                    <el-button link type="primary" @click="showApkDetails(scope.row)" v-if="scope.row.apkIds" size="small">
                        查看APK ({{ scope.row.apkIds ? scope.row.apkIds.split(',').length : 0 }})
                    </el-button>
                </template>
            </el-table-column>
            <el-table-column label="推送文件" align="center" width="120" show-overflow-tooltip>
                <template #default="scope">
                    <el-button link type="primary" @click="showFileDetails(scope.row)" v-if="scope.row.fileIds" size="small">
                        查看文件 ({{ scope.row.fileIds ? scope.row.fileIds.split(',').length : 0 }})
                    </el-button>
                </template>
            </el-table-column>
            <el-table-column label="开始时间" align="center" prop="startTime" width="140" show-overflow-tooltip sortable>
                <template #default="scope">
                    <span>{{ parseTime(scope.row.startTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
                </template>
            </el-table-column>
            <el-table-column label="结束时间" align="center" prop="endTime" width="140" show-overflow-tooltip sortable>
                <template #default="scope">
                    <span>{{ parseTime(scope.row.endTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
                </template>
            </el-table-column>
            <el-table-column label="应安装数" align="center" prop="installApkCount" width="100" show-overflow-tooltip />
            <el-table-column label="实际安装数" align="center" prop="installedApkCount" width="100" show-overflow-tooltip />
            <el-table-column label="任务完成度" align="center" min-width="100" show-overflow-tooltip>
                <template #default="scope">
                    <el-progress :percentage="parseFloat(scope.row.completionPercentage || 0)"
                        :color="getProgressColor(scope.row.completionPercentage)" />
                </template>
            </el-table-column>
            
            <el-table-column label="操作" align="center" class-name="small-padding fixed-width" fixed="right" width="120">
                <template #default="scope">
                    <!-- <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)"
                        v-hasPermi="['android:log:edit']">修改</el-button> -->
                    <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)"
                        v-hasPermi="['android:log:remove']" size="small">删除</el-button>
                </template>
            </el-table-column>
        </el-table>

        <pagination v-show="total >= 0" :total="total" v-model:page="queryParams.pageNum"
            v-model:limit="queryParams.pageSize" @pagination="getList" />

        <!-- 添加或修改推送日志对话框 -->
        <el-dialog :title="title" v-model="open" width="500px" append-to-body>
            <el-form ref="logRef" :model="form" :rules="rules" label-width="80px">
                <el-form-item label="推送人" prop="nickName">
                    <el-input v-model="form.nickName" placeholder="请输入推送人" />
                </el-form-item>
                <el-form-item label="设备所有id" prop="deviceIds">
                    <el-input v-model="form.deviceIds" type="textarea" placeholder="请输入内容" />
                </el-form-item>
                <el-form-item label="所有apkid" prop="apkIds">
                    <el-input v-model="form.apkIds" type="textarea" placeholder="请输入内容" />
                </el-form-item>
                <el-form-item label="开始时间" prop="startTime">
                    <el-date-picker clearable v-model="form.startTime" type="date" value-format="YYYY-MM-DD"
                        placeholder="请选择开始时间">
                    </el-date-picker>
                </el-form-item>
                <el-form-item label="应安装数" prop="installApkCount">
                    <el-input v-model="form.installApkCount" placeholder="请输入应安装数" />
                </el-form-item>
                <el-form-item label="实际安装数" prop="installedApkCount">
                    <el-input v-model="form.installedApkCount" placeholder="请输入实际安装数" />
                </el-form-item>
                <el-form-item label="任务完成度" prop="completionPercentage">
                    <el-input v-model="form.completionPercentage" placeholder="请输入任务完成度" />
                </el-form-item>
                <el-form-item label="结束时间" prop="endTime">
                    <el-date-picker clearable v-model="form.endTime" type="date" value-format="YYYY-MM-DD"
                        placeholder="请选择结束时间">
                    </el-date-picker>
                </el-form-item>
                <el-form-item label="剩余apk数" prop="remainingCount">
                    <el-input v-model="form.remainingCount" placeholder="请输入剩余apk数" />
                </el-form-item>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" @click="submitForm">确 定</el-button>
                    <el-button @click="cancel">取 消</el-button>
                </div>
            </template>
        </el-dialog>

        <!-- 设备详情对话框 -->
        <el-dialog v-model="deviceDetailOpen" title="设备详情" width="1000px" style="height: 600px;" append-to-body>
            <el-table :data="deviceDetailList" border class="deviceDetail_height" size="small">
                <el-table-column type="index" label="序号" width="60" align="center" fixed="left" stripe/>
                <el-table-column label="在线状态" prop="status" width="80" align="center">
                    <template #default="scope">
                        <el-tag :type="scope.row.status === 1 ? 'success' : 'info'">
                            {{ scope.row.status === 1 ? '在线' : '离线' }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column label="设备型号" prop="model" width="150" show-overflow-tooltip />
                <el-table-column label="设备SN" prop="sn" width="120" show-overflow-tooltip />
                <el-table-column label="IP地址" prop="ip" width="120" show-overflow-tooltip />
                <el-table-column label="MAC地址" prop="mac" width="150" show-overflow-tooltip />
                <el-table-column label="所属组" prop="groupName" show-overflow-tooltip />
            </el-table>
        </el-dialog>

        <!-- APK详情对话框 -->
        <el-dialog v-model="apkDetailOpen" title="文件详情" width="800px" style="height: 600px;" append-to-body>
            <el-table :data="apkDetailList" size="small" border class="apkDetail_height" stripe>
                <el-table-column type="index" label="序号" width="60" align="center" />
                <el-table-column label="图标" width="80" align="center">
                    <template #default="scope">
                        <div style="display: flex; justify-content: center; align-items: center;">
                            <img :src="`http://${scope.row.apkImage}`" alt="图标" style="width: 20px; height: 20px; border-radius: 4px;" @error="e => e.target.src = defaultImg" v-if="scope.row.apkImage" />
                            <img :src="defaultImg" alt="图标" style="width: 20px; height: 20px; border-radius: 4px;" v-else />
                        </div>
                    </template>
                </el-table-column>
                <el-table-column label="文件名称" prop="apkName" show-overflow-tooltip />
                <el-table-column label="包名" prop="apkPackage" show-overflow-tooltip />
                <el-table-column label="版本" prop="version" show-overflow-tooltip />
            </el-table>
        </el-dialog>
        <!-- 文件详情对话框 -->
        <el-dialog v-model="fileDetailOpen" title="文件详情" width="800px" style="height: 600px;" append-to-body>
            <el-table :data="fileDetailList" size="small" border class="apkDetail_height">
                <el-table-column type="index" label="序号" width="60" align="center" />
                <el-table-column label="文件名称" prop="apkName" show-overflow-tooltip />
                <el-table-column label="下载位置" prop="apkPath" show-overflow-tooltip />
            </el-table>
        </el-dialog>

        <!-- 剩余设备对话框 -->
        <el-dialog v-model="remainingDeviceOpen" :title="remainingDeviceTitle" width="1200px" style="height: 700px;" append-to-body>
            <!-- 操作按钮区域 -->
            <div class="remaining-device-actions" style="margin-bottom: 15px;">
                <el-button type="primary" @click="handleSelectAll" size="small">
                    {{ isAllSelected ? '取消全选' : '全选' }}
                </el-button>
                <el-button type="success" @click="handleBatchPush" :disabled="selectedRemainingDevices.length === 0" size="small">
                    批量推送 ({{ selectedRemainingDevices.length }})
                </el-button>
            </div>

            <el-table :data="remainingDeviceList" size="small" border class="remainingDevice_height"
                @selection-change="handleRemainingDeviceSelectionChange" stripe>
                <el-table-column type="selection" width="50" align="center" :selectable="checkDeviceSelectable" />
                <el-table-column type="index" label="序号" width="60" align="center" />
                <el-table-column label="在线状态" prop="status" width="80" align="center">
                    <template #default="scope">
                        <el-tag :type="scope.row.status === 1 ? 'success' : 'info'">
                            {{ scope.row.status === 1 ? '在线' : '离线' }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column label="SN" prop="sn" width="120" show-overflow-tooltip />
                <el-table-column label="MAC" prop="mac" width="120" show-overflow-tooltip />
                <el-table-column label="设备名称" prop="deviceName" width="120" show-overflow-tooltip />
                <el-table-column label="设备型号" prop="model" show-overflow-tooltip />
                <el-table-column label="IP地址" prop="ip" width="120" show-overflow-tooltip />
                <el-table-column label="未安装APK" width="150" show-overflow-tooltip>
                    <template #default="scope">
                        <div class="apk-list">
                            <div v-for="apk in scope.row.apkList" :key="apk.apkId" class="apk-item">
                                <img :src="`http://${apk.apkImage}`" alt="APK图标"
                                    style="width: 20px; height: 20px; border-radius: 4px; margin-right: 5px;"
                                    @error="e => e.target.src = defaultImg" />
                                <span>{{ apk.apkName }}</span>
                            </div>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column label="操作" width="100" align="center" fixed="right">
                    <template #default="scope">
                        <el-button type="primary" size="small" @click="handleSinglePush(scope.row)"
                            :disabled="scope.row.status !== 1">
                            推送
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
        </el-dialog>
    </div>
</template>

<script setup name="Log">
import { listLog, getLog, delLog, addPushLog, updateLog,getDeviceByPushLogId,getPushApkByPushLogId,getPushFileByPushLogId,getRemainingDeviceAndApk } from "@/api/pushLog.js";
import {sendMessageToAndroid} from '@/api/pcUseServerTodevice.js'
const { proxy } = getCurrentInstance();
import defaultImg from '@/assets/images/wenjian.png'

const logList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

// 详情对话框相关
const deviceDetailOpen = ref(false);
const apkDetailOpen = ref(false);
const fileDetailOpen = ref(false);
const deviceDetailList = ref([]);
const apkDetailList = ref([]);
const fileDetailList = ref([]);
// 剩余设备对话框相关
const remainingDeviceOpen = ref(false);
const remainingDeviceList = ref([]);
const remainingDeviceTitle = ref('');
const selectedRemainingDevices = ref([]);
const isAllSelected = ref(false);

const data = reactive({
    form: {},
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        nickName: null,
        deviceIds: null,
        apkIds: null,
        startTime: null,
        installApkCount: null,
        installedApkCount: null,
        completionPercentage: null,
        endTime: null,
        remainingCount: null
    },
    rules: {
    }
});

const { queryParams, form, rules } = toRefs(data);
import { useRoute } from "vue-router";
onMounted(() => {
  const route = useRoute();
  if(route.query.id) {
    queryParams.value.id = route.query.id;
  }
    getList();
});
/** 查询推送日志列表 */
function getList() {
    loading.value = true;
    listLog(queryParams.value).then(response => {
        logList.value = response.rows;
        total.value = response.total;
        loading.value = false;
    });
}

// 取消按钮
function cancel() {
    open.value = false;
    reset();
}

// 表单重置
function reset() {
    form.value = {
        id: null,
        nickName: null,
        deviceIds: null,
        apkIds: null,
        startTime: null,
        installApkCount: null,
        installedApkCount: null,
        completionPercentage: null,
        endTime: null,
        remainingCount: null
    };
    proxy.resetForm("logRef");
}

/** 搜索按钮操作 */
function handleQuery() {
    queryParams.value.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
    ids.value = selection.map(item => item.id);
    single.value = selection.length != 1;
    multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
    reset();
    open.value = true;
    title.value = "添加推送日志";
}

/** 修改按钮操作 */
function handleUpdate(row) {
    reset();
    const _id = row.id || ids.value
    getLog(_id).then(response => {
        form.value = response.data;
        open.value = true;
        title.value = "修改推送日志";
    });
}

/** 提交按钮 */
function submitForm() {
    proxy.$refs["logRef"].validate(valid => {
        if (valid) {
            if (form.value.id != null) {
                updateLog(form.value).then(() => {
                    proxy.$modal.msgSuccess("修改成功");
                    open.value = false;
                    getList();
                });
            } else {
                addPushLog(form.value).then(() => {
                    proxy.$modal.msgSuccess("新增成功");
                    open.value = false;
                    getList();
                });
            }
        }
    });
}

/** 删除按钮操作 */
function handleDelete(row) {
    const _ids = row.id || ids.value;
    proxy.$modal.confirm('是否确认删除推送日志编号为"' + _ids + '"的数据项？').then(function () {
        return delLog(_ids);
    }).then(() => {
        getList();
        proxy.$modal.msgSuccess("删除成功");
    }).catch(() => { });
}

/** 导出按钮操作 */
function handleExport() {
    proxy.download('android/log/export', {
        ...queryParams.value
    }, `log_${new Date().getTime()}.xlsx`)
}

/** 获取进度条颜色 */
function getProgressColor(percentage) {
    const percent = parseFloat(percentage || 0);
    if (percent >= 90) return '#67c23a';
    if (percent >= 70) return '#e6a23c';
    if (percent >= 50) return '#f56c6c';
    return '#909399';
}

/** 显示设备详情 */
async function showDeviceDetails(row) {
    if (!row.deviceIds) {
        proxy.$modal.msgWarning("该日志没有设备信息");
        return;
    }

    try {
        // 调用API获取设备详情
        const response = await getDeviceByPushLogId(row.id);
        console.log('推送日志设备详情:', response);

        if (response.data && response.data.length > 0) {
            deviceDetailList.value = response.data;
            deviceDetailOpen.value = true;
        } else {
            proxy.$modal.msgWarning("未找到该推送日志的设备信息");
        }
    } catch (error) {
        console.error('获取设备详情失败:', error);
        proxy.$modal.msgError("获取设备详情失败");
    }
}

/** 显示APK详情 */
async function showApkDetails(row) {
    if (!row.apkIds) {
        proxy.$modal.msgWarning("该日志没有APK信息");
        return;
    }

    try {
        // 调用API获取APK详情
        const response = await getPushApkByPushLogId(row.id);
        console.log('推送日志APK详情:', response);

        if (response.data && response.data.length > 0) {
            apkDetailList.value = response.data;
            apkDetailOpen.value = true;
        } else {
            proxy.$modal.msgWarning("未找到该推送日志的APK信息");
        }
    } catch (error) {
        console.error('获取APK详情失败:', error);
        proxy.$modal.msgError("获取APK详情失败");
    }
}
/** 显示文件详情 */
async function showFileDetails(row) {
    if (!row.apkIds) {
        proxy.$modal.msgWarning("该日志没有APK信息");
        return;
    }

    try {
        // 调用API获取APK详情
        const response = await getPushFileByPushLogId(row.id);
        console.log('推送日志APK详情:', response);

        if (response.data && response.data.length > 0) {
            fileDetailList.value = response.data;
            fileDetailOpen.value = true;
        } else {
            proxy.$modal.msgWarning("未找到该推送日志的APK信息");
        }
    } catch (error) {
        console.error('获取APK详情失败:', error);
        proxy.$modal.msgError("获取文件详情失败");
    }
}

/** 显示剩余设备详情 */
async function showRemainingDevices(row) {
    console.log(row)
    if (!row.remainingCount || row.remainingCount === 0) {
        proxy.$modal.msgWarning("该推送日志没有剩余未安装的APK");
        return;
    }

    try {
        // 调用API获取剩余设备和APK详情
        const response = await getRemainingDeviceAndApk(row.id);
        console.log('剩余设备和APK详情:', response);

        if (response.data && response.data.length > 0) {
            remainingDeviceList.value = response.data;
            remainingDeviceTitle.value = `剩余未安装APK的设备 (${response.data.length})`;
            remainingDeviceOpen.value = true;
            // 重置选择状态
            selectedRemainingDevices.value = [];
            isAllSelected.value = false;
        } else {
            proxy.$modal.msgWarning("未找到剩余未安装APK的设备");
        }
    } catch (error) {
        console.error('获取剩余设备详情失败:', error);
        proxy.$modal.msgError("获取剩余设备详情失败");
    }
}

/** 检查设备是否可选择 */
function checkDeviceSelectable(row) {
    // 只有在线设备可以选择
    return row.status === 1;
}

/** 处理剩余设备选择变化 */
function handleRemainingDeviceSelectionChange(selection) {
    selectedRemainingDevices.value = selection;
    // 检查是否全选（只考虑在线设备）
    const onlineDevices = remainingDeviceList.value.filter(device => device.status === 1);
    isAllSelected.value = onlineDevices.length > 0 && selection.length === onlineDevices.length;
}

/** 处理全选/取消全选 */
function handleSelectAll() {
    // 这里需要通过ref获取表格实例，暂时用简单的逻辑
    if (isAllSelected.value) {
        // 取消全选
        selectedRemainingDevices.value = [];
        isAllSelected.value = false;
    } else {
        // 全选在线设备
        const onlineDevices = remainingDeviceList.value.filter(device => device.status === 1);
        selectedRemainingDevices.value = [...onlineDevices];
        isAllSelected.value = true;
    }
}

/** 单个设备推送 */
async function handleSinglePush(device) {
    console.log(device);
    if (device.status !== 1) {
        proxy.$modal.msgWarning("设备离线，无法推送");
        return;
    }

    try {
        await proxy.$modal.confirm(`确定要向设备 "${device.deviceName || device.sn}" 推送未安装的APK吗？`, '确认推送');
        // return;
        // 这里调用推送API
        sendMessageToAndroid({
            deviceIds: [device.id],
            type: "server",
            value: "download",
            checkedPackage: true,
            apkIds: device.apkList.map(apk => apk.id),
            isAllGroup: false
        }).then(res => {
            console.log(res);
            proxy.$modal.msgSuccess("推送任务已发送");
        }); 
        // await pushApkToDevice({ deviceId: device.id, apkIds: device.apkList.map(apk => apk.apkId) });
        // 可以选择刷新剩余设备列表或关闭对话框
        // remainingDeviceOpen.value = false;
    } catch (error) {
        if (error !== 'cancel') {
            console.error('推送失败:', error);
            proxy.$modal.msgError("推送失败");
        }
    }
}

/** 批量推送 */
async function handleBatchPush() {
    if (selectedRemainingDevices.value.length === 0) {
        proxy.$modal.msgWarning("请先选择要推送的设备");
        return;
    }

    try {
        await proxy.$modal.confirm(`确定要向选中的 ${selectedRemainingDevices.value.length} 个设备推送未安装的APK吗？`, '确认批量推送');
        // 循环调用推送API，遍历每个设备
        const pushPromises = selectedRemainingDevices.value.map(device => {
            const apkIds = device.apkList ? device.apkList.map(apk => apk.id) : [];
            return sendMessageToAndroid({
                deviceIds: [device.id],
                type: "server",
                value: "download",
                checkedPackage: true,
                apkIds: apkIds,
                isAllGroup: false
            }).then(res => {
                console.log(`设备 ${device.deviceName || device.sn} 推送结果:`, res);
                return { device, success: true, result: res };
            }).catch(error => {
                console.error(`设备 ${device.deviceName || device.sn} 推送失败:`, error);
                return { device, success: false, error };
            });
        });

        // 等待所有推送完成
        const results = await Promise.all(pushPromises);
        // 统计推送结果
        const successCount = results.filter(r => r.success).length;
        const failCount = results.filter(r => !r.success).length;
        if (failCount === 0) {
            proxy.$modal.msgSuccess(`已成功向 ${successCount} 个设备发送推送任务`);
        } else {
            proxy.$modal.msgWarning(`推送完成：成功 ${successCount} 个，失败 ${failCount} 个`);
        }
        // 可以选择刷新剩余设备列表或关闭对话框
        // remainingDeviceOpen.value = false;
    } catch (error) {
        if (error !== 'cancel') {
            console.error('批量推送失败:', error);
            proxy.$modal.msgError("批量推送失败");
        }
    }
}

/** 图片加载错误处理 */
function handleImageError(event) {
    event.target.style.display = 'none';
}

// getList();
</script>

<style lang="scss" scoped>
.lf-table-content-height {
    height: calc(100vh - 235px) !important;
}
.deviceDetail_height {
    height: calc(800px - 270px) !important;
}

.apkDetail_height {
    height: calc(800px - 270px) !important;
}

.remainingDevice_height {
    height: calc(800px - 270px) !important;
}

:deep(.el-form-item) {
    margin-bottom: 10px !important;
}

:deep(.el-table__inner-wrapper:before) {
    height: 0.8px !important;
}

:deep(.el-table__inner-wrapper) {
    border-radius: 8px;
}

:deep(.el-table) {
    border-radius: 4px;
}

:deep(.el-table th.el-table__cell) {
    background-color: #f5f7fa;
    color: #606266;
    font-weight: 600;
}

:deep(.el-table .el-table__row:hover > td) {
    background-color: #f5f7fa;
}

:deep(.el-progress-bar__outer) {
    border-radius: 10px;
}

:deep(.el-progress-bar__inner) {
    border-radius: 10px;
}

.apk-list {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.apk-item {
    display: flex;
    align-items: center;
    font-size: 12px;
    color: #606266;
}
</style>
