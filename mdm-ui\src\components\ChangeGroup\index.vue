<template>
    <div class="change-group">
        <!-- 显示当前组名，点击可修改 -->
        <el-button type="primary" text @click="openDialog" :title="`点击修改组名: ${row.groupName || '未分组'}`"
            class="group-name-btn" v-hasPerdisa="['group:disabled:edit']" v-hasPermi="['group:show:edit']"
            :disabled="row.status == 2">
            {{ row.groupName || '未分组' }}
        </el-button>

        <!-- 修改组名对话框 -->
        <el-dialog v-model="dialogVisible" title="修改设备组" width="800px" append-to-body @close="handleClose">
            <div class="dialog-content">
                <!-- 左侧：组树选择 -->
                <div class="group-tree-section">
                    <h4>选择组</h4>
                    <el-input v-model="filterText" placeholder="搜索组名" clearable style="margin-bottom: 10px;" />
                    <el-tree ref="treeRef" :data="groupList" :props="treeProps" :filter-node-method="filterNode"
                        highlight-current default-expand-all node-key="id" @node-click="handleNodeClick"
                        class="group-tree">
                        <template #default="{ node, data }">
                            <div class="tree-node">
                                <span>{{ node.label }}</span>
                                <el-tag v-if="data.id === row.groupId" type="success" size="small"
                                    style="margin-left: 8px;">
                                    当前组
                                </el-tag>
                            </div>
                        </template>
                    </el-tree>
                </div>

                <!-- 右侧：选中组的策略展示 -->
                <div class="group-policy-section">
                    <h4>{{ selectedGroupName || '请选择组' }} - 组策略</h4>
                    <div class="policy-list" v-loading="policyLoading">
                        <div v-if="groupPolicies.length === 0" class="empty-policy">
                            <el-empty description="该组暂无策略" :image-size="60" />
                        </div>
                        <div v-else class="policy-items">
                            <div v-for="policy in groupPolicies" :key="policy.id" class="policy-item">
                                <div class="policy-icon">
                                    <img :src="`http://${policy.apkImg}`" alt="APK图标" @error="handleImageError" />
                                </div>
                                <div class="policy-info">
                                    <div class="policy-name">{{ policy.apkName }}</div>
                                    <div class="policy-package">{{ policy.apkPackage }}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="handleClose">取消</el-button>
                    <el-button type="primary" @click="confirmChange"
                        :disabled="!selectedGroupId || selectedGroupId === row.groupId" :loading="updating">
                        确认修改
                    </el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script setup>
import { ref, watch, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { listGroup, updateDevice, updateDeviceGroup } from '@/api/group'
import { getGroupApk } from '@/api/group_apk'
import { defineProps, defineEmits } from 'vue'
// Props
const props = defineProps({
    row: {
        type: Object,
        required: true,
        default: () => ({})
    }
})

// Emits
const emit = defineEmits(['refresh'])

// 响应式数据
const dialogVisible = ref(false)
const filterText = ref('')
const groupList = ref([])
const selectedGroupId = ref('')
const selectedGroupName = ref('')
const groupPolicies = ref([])
const policyLoading = ref(false)
const updating = ref(false)
const treeRef = ref()

// 树组件配置
const treeProps = {
    children: 'children',
    label: 'groupName',
    value: 'id'
}

// 监听搜索文本变化
watch(filterText, (val) => {
    treeRef.value?.filter(val)
})

// 打开对话框
const openDialog = async () => {
    dialogVisible.value = true
    await loadGroupList()
    // 默认选中当前组
    selectedGroupId.value = props.row.groupId || ''
    selectedGroupName.value = props.row.groupName || ''
    await loadGroupPolicies(props.row.groupId)

    // 设置当前选中的节点
    nextTick(() => {
        if (treeRef.value && props.row.groupId) {
            treeRef.value.setCurrentKey(props.row.groupId)
        }
    })
}

// 加载组列表
const loadGroupList = async () => {
    try {
        const response = await listGroup()
        // 使用 handleTree 处理树形数据
        groupList.value = handleTree(response.data || response)
    } catch (error) {
        console.error('加载组列表失败:', error)
        ElMessage.error('加载组列表失败')
        groupList.value = []
    }
}

// 处理树形数据的函数（从父组件借用）
const handleTree = (data, id = 'id', parentId = 'parentId', children = 'children') => {
    const config = {
        id: id || 'id',
        parentId: parentId || 'parentId',
        childrenList: children || 'children'
    }

    const childrenListMap = {}
    const nodeIds = {}
    const tree = []

    for (const d of data) {
        const parentId = d[config.parentId]
        if (childrenListMap[parentId] == null) {
            childrenListMap[parentId] = []
        }
        nodeIds[d[config.id]] = d
        childrenListMap[parentId].push(d)
    }

    for (const d of data) {
        const parentId = d[config.parentId]
        if (nodeIds[parentId] == null) {
            tree.push(d)
        }
    }

    for (const t of tree) {
        adaptToChildrenList(t)
    }

    function adaptToChildrenList(o) {
        if (childrenListMap[o[config.id]] !== null) {
            o[config.childrenList] = childrenListMap[o[config.id]]
        }
        if (o[config.childrenList]) {
            for (const c of o[config.childrenList]) {
                adaptToChildrenList(c)
            }
        }
    }
    return tree
}

// 加载组策略
const loadGroupPolicies = async (groupId) => {
    if (!groupId) {
        groupPolicies.value = []
        return
    }

    policyLoading.value = true
    try {
        const response = await getGroupApk(groupId)
        groupPolicies.value = response.data || []
        console.log(groupPolicies.value);
    } catch (error) {
        console.error('加载组策略失败:', error)
        groupPolicies.value = []
    } finally {
        policyLoading.value = false
    }
}

// 树节点点击事件
const handleNodeClick = async (data) => {
    selectedGroupId.value = data.id
    selectedGroupName.value = data.groupName
    await loadGroupPolicies(data.id)
}

// 树节点过滤方法
const filterNode = (value, data) => {
    if (!value) return true
    return data.groupName.includes(value)
}

// 图片加载错误处理
const handleImageError = (event) => {
    // 设置一个简单的默认图标或隐藏图片
    event.target.style.display = 'none'
}

// 确认修改组
const confirmChange = async () => {
    if (!selectedGroupId.value || selectedGroupId.value === props.row.groupId) {
        return
    }
    try {
        await ElMessageBox.confirm(
            `确定要将设备 "${props.row.deviceName || props.row.sn}" 从 "${props.row.groupName}" 组 移动到 "${selectedGroupName.value}" 组 吗？`,
            '确认修改',
            {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }
        )
        updating.value = true
        // 更新设备的组信息
        const updateData = {
            deviceId: props.row.id,
            groupId: selectedGroupId.value,
            // groupName: selectedGroupName.value
        }
        // await updateDevice(updateData)
        await updateDeviceGroup(updateData).then(response => {
                // setTimeout(() => {
                //     emit('refresh')
                // }, 500)
        })
        ElMessage.success('修改组成功')
        dialogVisible.value = false

        // 通知父组件刷新数据


    } catch (error) {
        if (error !== 'cancel') {
            console.error('修改组失败:', error)
            ElMessage.error('修改组失败')
        }
    } finally {
        updating.value = false
        
    }
}

// 关闭对话框
const handleClose = () => {
    dialogVisible.value = false
    filterText.value = ''
    selectedGroupId.value = ''
    selectedGroupName.value = ''
    groupPolicies.value = []
}
</script>

<style lang="scss" scoped>
.change-group {
    .group-name-btn {
        font-weight: bold;
        font-style: italic;

        &:hover {
            text-decoration: underline;
        }
    }
}

.dialog-content {
    display: flex;
    gap: 20px;
    height: 500px;

    .group-tree-section {
        flex: 1;
        border-right: 1px solid #e4e7ed;
        padding-right: 20px;

        h4 {
            margin: 0 0 15px 0;
            color: #303133;
            font-size: 16px;
        }

        .group-tree {
            height: 400px;
            overflow-y: auto;

            .tree-node {
                display: flex;
                align-items: center;
                width: 100%;
            }
        }
    }

    .group-policy-section {
        flex: 1;
        padding-left: 20px;

        h4 {
            margin: 0 0 15px 0;
            color: #303133;
            font-size: 16px;
        }

        .policy-list {
            height: 400px;
            overflow-y: auto;

            .empty-policy {
                display: flex;
                justify-content: center;
                align-items: center;
                height: 100%;
            }

            .policy-items {
                .policy-item {
                    display: flex;
                    align-items: center;
                    padding: 12px;
                    border: 1px solid #e4e7ed;
                    border-radius: 8px;
                    margin-bottom: 10px;
                    transition: all 0.3s;

                    &:hover {
                        background-color: #f5f7fa;
                        border-color: #409eff;
                    }

                    .policy-icon {
                        width: 40px;
                        height: 40px;
                        margin-right: 12px;
                        display: flex;
                        align-items: center;
                        justify-content: center;

                        img {
                            width: 32px;
                            height: 32px;
                            border-radius: 4px;
                        }
                    }

                    .policy-info {
                        flex: 1;

                        .policy-name {
                            font-weight: bold;
                            color: #303133;
                            margin-bottom: 4px;
                        }

                        .policy-package {
                            font-size: 12px;
                            color: #909399;
                        }
                    }
                }
            }
        }
    }
}

.dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}
</style>