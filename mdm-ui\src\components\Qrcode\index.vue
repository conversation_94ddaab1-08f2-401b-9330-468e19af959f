<template>
  <div v-if="visible" class="qrcode-dialog" @click.self="close">
    <div class="qrcode-container">
      <div class="qrcode-item">
        <p>用户名</p>
        <canvas ref="userNameCanvas"></canvas>
        <p>{{ userName }}</p>
      </div>
      <div class="qrcode-item">
        <p>密码</p>
        <canvas ref="passwordCanvas"></canvas>
        <p>{{ password }}</p>
      </div>
      <div class="qrcode-item">
        <p>服务器IP</p>
        <canvas ref="serverIpCanvas"></canvas>
        <p>{{ serverIp }}</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { defineProps, defineExpose, ref, watch, onMounted, nextTick } from 'vue'
import QRCode from 'qrcode'

const props = defineProps({
  userName: String,
  password: String,
  serverIp: String
})

const visible = ref(false)

const userNameCanvas = ref(null)
const passwordCanvas = ref(null)
const serverIpCanvas = ref(null)

function show() {
  visible.value = true
  nextTick(() => {
    generateQRCodes()
  })
}

function close() {
  visible.value = false
}

function generateQRCodes() {
  if (userNameCanvas.value) {
    QRCode.toCanvas(userNameCanvas.value, props.userName || '', { width: 200 })
  }
  if (passwordCanvas.value) {
    QRCode.toCanvas(passwordCanvas.value, props.password || '', { width: 200 })
  }
  if (serverIpCanvas.value) {
    QRCode.toCanvas(serverIpCanvas.value, props.serverIp || '', { width: 200 })
  }
}

defineExpose({ show, close })
</script>

<style scoped>
.qrcode-dialog {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.qrcode-container {
  background: white;
  padding: 40px;
  border-radius: 8px;
  display: flex;
  gap: 80px;
}

.qrcode-item {
  text-align: center;
  opacity: 0.8;
  transition: all 0.3s ease-in-out;
}

.qrcode-item:hover {
  opacity: 1;
}
p {
  margin: 0;
  font-size: 14px;
}
</style>
