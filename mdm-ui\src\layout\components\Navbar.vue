<template>
  <div class="navbar">
    <!-- <hamburger id="hamburger-container" :is-active="appStore.sidebar.opened" class="hamburger-container" @toggleClick="toggleSideBar" /> -->
    <breadcrumb id="breadcrumb-container" class="breadcrumb-container" v-if="!settingsStore.topNav" />
    <top-nav id="topmenu-container" class="topmenu-container" v-if="settingsStore.topNav" />

    <div class="right-menu">
      <template v-if="appStore.device !== 'mobile'">
        <header-search id="header-search" class="right-menu-item" />
        <screenfull id="screenfull" class="right-menu-item hover-effect" />
      </template>
      <!--  -->
      <div class="avatar-container">
        <el-dropdown @command="handleCommand" class="right-menu-item hover-effect" trigger="click">
          <div class="avatar-wrapper">
            <img :src="userStore.avatar" class="user-avatar" />
            <span class="user-nickname">{{ userStore.nickName }}</span>
          </div>
          <template #dropdown>
            <el-dropdown-menu>
              <router-link to="/user/profile">
                <el-dropdown-item>个人中心</el-dropdown-item>
              </router-link>
              <!-- <el-dropdown-item command="setLayout" v-if="settingsStore.showSettings">
                <span>布局设置</span>
              </el-dropdown-item> -->
              <el-dropdown-item divided command="logout">
                <span>退出登录</span>
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
      <!--  -->
      <div class="about-us">
        <el-dropdown @command="handleCommand" class="right-menu-item hover-effect" trigger="click">
          <div class="avatar-wrapper">
            <span class="user-nickname"><el-icon class="el-icon--left">
                <Warning />
              </el-icon>关于<el-icon class="el-icon--right"><arrow-down /></el-icon>
            </span>
          </div>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="a" @click="authorize = true">授权信息</el-dropdown-item>
              <el-dropdown-item command="b" @click="version = true">版本信息</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>
    <el-dialog v-model="authorize" title="授权信息" width="500" append-to-body align-center>
      <template #footer>
        <div class="dialog-footer dialog-footer-1">
          <img src="../../assets/logo/logo.png" alt="">
          <H3 style="color: #606266; font-weight: bold;">已授权</H3>
        </div>
      </template>
    </el-dialog>
    <el-dialog v-model="version" title="版本信息" width="500" append-to-body align-center>
      <template #footer>
        <div class="dialog-footer dialog-footer-2">
          <H3 style="color: #606266; font-weight: bold;">版本信息</H3>
          <span style="color: #606266;font-size: 14px;">前端版本号：1.0.1</span>
          <span style="color: #606266;font-size: 14px;">后端版本号：1.0.1</span>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ElMessageBox } from 'element-plus'
import Breadcrumb from '@/components/Breadcrumb'
import TopNav from '@/components/TopNav'
import Hamburger from '@/components/Hamburger'
import Screenfull from '@/components/Screenfull'
import SizeSelect from '@/components/SizeSelect'
import HeaderSearch from '@/components/HeaderSearch'
import RuoYiGit from '@/components/RuoYi/Git'
import RuoYiDoc from '@/components/RuoYi/Doc'
import useAppStore from '@/store/modules/app'
import useUserStore from '@/store/modules/user'
import useSettingsStore from '@/store/modules/settings'
import { Warning, ArrowDown } from '@element-plus/icons-vue'
import { ref } from 'vue'

const appStore = useAppStore()
const userStore = useUserStore()
const settingsStore = useSettingsStore()

const authorize = ref(false)
const version = ref(false)

function toggleSideBar() {
  appStore.toggleSideBar()
}

function handleCommand(command) {
  switch (command) {
    case "setLayout":
      setLayout();
      break;
    case "logout":
      logout();
      break;
    default:
      break;
  }
}

function logout() {
  ElMessageBox.confirm('确定注销并退出系统吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    userStore.logOut().then(() => {
      location.href = '/index';
    })
  }).catch(() => { });
}

const emits = defineEmits(['setLayout'])
function setLayout() {
  emits('setLayout');
}
</script>

<style lang='scss' scoped>
@import "@/assets/styles/variables.module.scss";

.navbar {
  height: 50px;
  overflow: hidden;
  position: relative;
  background: #fff;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  background: $base-navbar-background !important;
  // z-index: 10000;
  z-index: 1000;

  .hamburger-container {
    line-height: 46px;
    height: 100%;
    float: left;
    cursor: pointer;
    transition: background 0.3s;
    -webkit-tap-highlight-color: transparent;

    &:hover {
      background: rgba(0, 0, 0, 0.025);
    }
  }

  .breadcrumb-container {
    float: left;
  }

  .topmenu-container {
    position: absolute;
    left: 50px;
  }

  .errLog-container {
    display: inline-block;
    vertical-align: top;
  }

  .right-menu {
    float: right;
    height: 100%;
    line-height: 50px;
    display: flex;

    &:focus {
      outline: none;
    }

    .right-menu-item {
      display: inline-block;
      padding: 0 8px;
      height: 100%;
      font-size: 18px;
      color: #fff;
      vertical-align: text-bottom;

      &.hover-effect {
        cursor: pointer;
        transition: background 0.3s;

        &:hover {
          background: rgba(0, 0, 0, 0.025);
        }
      }
    }

    .avatar-container {
      margin-right: 4px;

      .avatar-wrapper {
        margin-top: 10px;
        right: 5px;
        position: relative;

        .user-avatar {
          cursor: pointer;
          width: 30px;
          height: 30px;
          border-radius: 50%;
        }

        .user-nickname {
          position: relative;
          left: 5px;
          bottom: 10px;
          font-size: 14px;
          font-weight: bold;
        }

        i {
          cursor: pointer;
          position: absolute;
          right: 2px;
          top: 25px;
          font-size: 12px;
        }
      }
    }

    .about-us {
      margin-right: 4px;
      height: 100%;

      .avatar-wrapper {
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;

        .user-nickname {
          position: relative;
          bottom: 0px;
          top: 0px;
          left: 0;
          right: 0;
          margin: auto;
          font-size: 14px;
          font-weight: bold;
        }

        i {
          cursor: pointer;
          right: 2px;
          top: 2px;
          font-size: 12px;
        }
      }
    }
  }
}
.dialog-footer {
  width: 100%;
}
.dialog-footer-1 {
  height: 400px;
  background: url("../../assets/images/bg.png") no-repeat bottom center !important;
  background-size: contain;
  display: flex;
  flex-direction: column;
  align-items: center;
  img {
    width: 300px;
    height: auto;

  }
}
.dialog-footer-2 {
  height: 400px;
  background: url("../../assets/images/bg.png") no-repeat bottom center !important;
  background-size: contain;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  img {
    width: 300px;
    height: auto;

  }
}
</style>
