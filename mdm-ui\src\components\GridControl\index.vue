<template>
    <el-dropdown trigger="click" :hide-on-click="false" style="padding-left: 12px" placement="bottom-end"
        max-height="200" >
        <el-button text :bg="bg" :size="size" icon="Grid" style="font-size: 20px;color: black;" v-hasPerdisa="['group:disabled:control']" v-hasPermi="['group:show:control']" :disabled="ids[0].status == 2"></el-button>
        <template #dropdown>
            <el-dropdown-menu>
                <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 4px;padding: 4px;">
                    <el-dropdown-item class="clearfix" v-for="(item, index) in gridList" :key="index"
                        @click.prevent="handleControl(item)" :disabled="item.disabled">
                        <el-button link :icon="item.icon" :v-hasPermi="[item.permission]" :disabled="item.disabled">{{
                            item.label }}</el-button>
                    </el-dropdown-item>
                </div>
            </el-dropdown-menu>
        </template>
    </el-dropdown>
    <el-drawer v-model="openDrawer" append-to-body title="远程控制" size="100%" @close="closeLongRange">
        <div class="remote-content">
            <div class="remote-content-left">
                <el-dropdown trigger="click" :hide-on-click="false" style="padding-left: 12px" placement="bottom-end"
                    max-height="200">
                    <el-button text :bg="bg" :size="size" icon="Grid" style="font-size: 20px;color: black;"></el-button>
                    <template #dropdown>
                        <el-dropdown-menu>
                            <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 4px;padding: 4px;">
                                <el-dropdown-item class="clearfix" v-for="(item, index) in selfGridList" :key="index"
                                    @click="handleControl(item)" :disabled="item.disabled">
                                    <el-button link :icon="item.icon" :v-hasPermi="[item.permission]"
                                        :disabled="item.disabled">{{
                                            item.label }}</el-button>
                                </el-dropdown-item>
                            </div>
                        </el-dropdown-menu>
                    </template>
                </el-dropdown>
            </div>
            <canvas ref="canvas" id="imageCanvas" style="display: block;"></canvas>
        </div>

    </el-drawer>

</template>
<script setup>
import {  addLog } from "@/api/log/operate.js";
import { defineProps, onMounted, watch } from 'vue';
import { ElNotification } from 'element-plus';
const socket = ref(null); // WebSocket对象
const props = defineProps({
    ids: {
        type: Object,
        default: []
    },
    size: {
        type: String,
        default: 'large'
    },
    bg: {
        type: Boolean,
        default: true
    }
});
const gridList = ref([
    { label: '解锁', icon: 'Unlock', permission: 'pc:group:edit', disabled: false },
    { label: '锁定', icon: 'Lock', permission: 'pc:group:edit', disabled: false },
    { label: '设备远程', icon: 'Connection', permission: 'pc:group:edit', disabled: false },
    { label: '重启', icon: 'SwitchButton', permission: 'pc:group:edit', disabled: false },
    { label: '发出声音', icon: 'Bell', permission: 'pc:group:edit', disabled: false },
    { label: '关闭声音', icon: 'MuteNotification', permission: 'pc:group:edit', disabled: false },
    // { label: '屏幕截图', icon: 'CameraFilled', permission: 'pc:group:edit', disabled: false },
    { label: '提高音量', icon: 'Plus', permission: 'pc:group:edit', disabled: false },
    { label: '降低音量', icon: 'Minus', permission: 'pc:group:edit', disabled: false },
    { label: '静音', icon: 'Mute', permission: 'pc:group:edit', disabled: false },
    { label: '禁用usb', icon: 'CircleClose', permission: 'pc:group:edit', disabled: false },
    { label: '启用usb', icon: 'CircleCheck', permission: 'pc:group:edit', disabled: false },
    { label: '禁用蓝牙', icon: 'CircleClose', permission: 'pc:group:edit', disabled: false },
    { label: '启用蓝牙', icon: 'CircleCheck', permission: 'pc:group:edit', disabled: false },
    { label: '禁用摄像头', icon: 'CircleClose', permission: 'pc:group:edit', disabled: false },
    { label: '启用摄像头', icon: 'CircleCheck', permission: 'pc:group:edit', disabled: false },
    // { label: '位置信息', icon: 'Location', permission: 'pc:group:edit', disabled: false }
]);
const selfGridList = ref([
    { label: '解锁', icon: 'Unlock', permission: 'pc:group:edit', disabled: false },
    { label: '锁定', icon: 'Lock', permission: 'pc:group:edit', disabled: false }
]);
const canvas = ref(null); // Canvas元素
const canvasWidth = ref(0); // 画布宽度
const canvasHeight = ref(0); // 画布高度
const openDrawer = ref(false); // 控制抽屉的打开状态
const isDragging = ref(false); // 是否正在拖动
let ctx;
const operateType = {
  control: "控制指令",
  install: "安装指令",
  update: "更新指令",
  uninstall: "卸载指令",
  reboot: "重启指令",
  shutdown: "关机指令",
  get: "get指令"
}
const subdivideType = {
  openSound: "发出声音",
  closeSound: "关闭声音",
  openScreen: "打开屏幕",
  closeScreen: "关闭屏幕",
  screenshot: "截图",
  control: "远程",
  shutdown: "重启",
  addVolume: "音量加",
  reduceVolume: "音量减",
  muteVolume: "静音",
  noUsb: "禁用usb",
  yesUsb: "启用usb",
  noBluetooch: "禁用蓝牙",
  yesBluetooch: "启用蓝牙",
  noCamera: "禁用摄像头",
  yesCamera: "启用摄像头",
  location: "获取位置",
}
onMounted(() => {
});
// 处理对设备的操作
function handleControl(item) {
    if (props.ids.length === 0) {
        ElNotification({
            title: '提示',
            message: '请选择要操作的设备',
            type: 'warning',
            position: 'top-left'
        });
        return;
    } else if (props.ids.length > 1) {
        ElNotification({
            title: '提示',
            message: '只能选择一台设备进行操作',
            type: 'warning',
            position: 'top-left'
        });
        return;
    }
    switch (item.label) {
        case '发出声音':
            sendCommand(JSON.stringify([{
                type: "set",
                value: "openSound"
            }]), "发出声音","control","openSound");
            break;
        case '关闭声音':
            sendCommand(JSON.stringify([{
                type: "set",
                value: "closeSound"
            }]), "关闭声音", "control", "closeSound");
            break;
        case '解锁':
            sendCommand(JSON.stringify([{
                type: "set",
                value: "openScreen"
            }]), "解锁","control", "openScreen");
            break;
        case '锁定':
            sendCommand(JSON.stringify([{
                type: "set",
                value: "closeScreen"
            }]), "锁定", "control", "closeScreen");
            break;
        case '屏幕截图':
            sendCommand(JSON.stringify([{
                type: "get",
                value: "screenshot"
            }]), "屏幕截图", "control", "screenshot");
            break;
        case "设备远程":
            sendCommand(JSON.stringify([{
                type: "control",
                value: ""
            }]), "设备远程", "get", "control");
            break;
        case '重启':
            sendCommand(JSON.stringify([{
                type: "set",
                value: "shutdown"
            }]), "重启", "reboot", "shutdown");
            break;
        case '提高音量':
            sendCommand(JSON.stringify([{
                type: "set",
                value: "addVolume"
            }]), "提高音量", "control", "addVolume");
            break;
        case '降低音量':
            sendCommand(JSON.stringify([{
                type: "set",
                value: "reduceVolume"
            }]), "降低音量", "control", "reduceVolume");
            break;
        case '静音':
            sendCommand(JSON.stringify([{
                type: "set",
                value: "muteVolume"
            }]), "静音", "control", "muteVolume");
            break;
        case '禁用usb':
            sendCommand(JSON.stringify([{
                type: "set",
                value: "noUsb"
            }]), "禁用usb", "control", "noUsb");
            break;
        case '启用usb':
            sendCommand(JSON.stringify([{
                type: "set",
                value: "yesUsb"
            }]), "启用usb", "control", "yesUsb");
            break;
        case '禁用蓝牙':
            sendCommand(JSON.stringify([{
                type: "set",
                value: "noBluetooch"
            }]), "禁用蓝牙", "control", "noBluetooch");
            break;
        case '启用蓝牙':
            sendCommand(JSON.stringify([{
                type: "set",
                value: "yesBluetooch"
            }]), "启用蓝牙", "control", "yesBluetooch");
            break;
        case '禁用摄像头':
            sendCommand(JSON.stringify([{
                type: "set",
                value: "noCamera"
            }]), "禁用摄像头", "control", "noCamera");
            break;
        case '启用摄像头':
            sendCommand(JSON.stringify([{
                type: "set",
                value: "yesCamera"
            }]), "启用摄像头", "control", "yesCamera");
            break;
        case '位置信息':
            sendCommand(JSON.stringify([{
                type: "get",
                value: "location"
            }]), "位置信息", "get", "location");
            break;
        default:
            ElNotification({
                title: '提示',
                message: '该功能暂未开放',
                type: 'warning'
            });
    }
}

const beginZuobiao = ref([]); // 鼠标按下时的X坐标
function sendCommand(res, label,operationType, subdivide) {
    const timeout = 1000; // 设定连接超时时间（毫秒）
    let didConnect = false;
    socket.value = new WebSocket(`ws://${props.ids[0].ip}:8080`);
    // 设置超时处理逻辑
    const timer = setTimeout(() => {
        if (!didConnect) {
            socket.value.close();
            ElNotification({
                title: '连接超时',
                message: '设备连接超时，请检查网络或设备状态',
                type: 'error',
                position: 'top-left',
            });
        }
    }, timeout);
    socket.value.onopen = function () {
        didConnect = true;
        clearTimeout(timer); // 连接成功，清除超时定时器
        console.log("WebSocket 已连接");
        socket.value.send(res)
        ElNotification({
            title: '发送指令',
            message: "" + label + " 指令已成功发送",
            type: 'success',
            position: 'top-left',
        });
        handleAddLog({
            type: operateType[operationType],
            subdivisionType: subdivideType[subdivide],
            isSuccess: "成功",
            deviceId: props.ids[0].id,
            content: "发送指令成功"
        });
    };
    socket.value.onmessage = function (event) {
        try {
            const data = JSON.parse(event.data); // 解析 JSON 字符串
            if (data.type === 'screen' && data.image) {
                openDrawer.value = true; // 打开抽屉
                const image = new Image();
                image.src = 'data:image/jpeg;base64,' + data.image; // 拼接成图片地址
                image.onload = function () {
                    const ctx = canvas.value.getContext('2d');
                    const parent = canvas.value.parentElement;
                    const parentHeight = parent.clientHeight;
                    const imgWidth = image.width;
                    const imgHeight = image.height;
                    const ratio = imgWidth / imgHeight;
                    canvasHeight.value = parentHeight;
                    canvasWidth.value = canvasHeight.value * ratio;
                    canvas.value.height = canvasHeight.value;
                    canvas.value.width = canvasWidth.value;
                    ctx.drawImage(image, 0, 0, canvasWidth.value, canvasHeight.value);

                    if (!isDragging.value) {
                        canvas.value.onmousedown = (e) => {
                            isDragging.value = true; // 设置为正在拖动状态
                            const { offsetX, offsetY } = e;
                            const x = parseInt(offsetX / canvasWidth.value * 100);
                            const y = parseInt(offsetY / canvasHeight.value * 100);
                            console.log(x, y);
                            beginZuobiao.value = [x, y]; // 记录鼠标按下时的坐标
                        };
                        canvas.value.onmousemove = (e) => {
                            if (isDragging.value) {
                                const { offsetX, offsetY } = e
                                const x = parseInt(offsetX / canvasWidth.value * 100);
                                const y = parseInt(offsetY / canvasHeight.value * 100);
                                // socket.value.send(JSON.stringify([{ type: "control", value: "" + x + "," + "" + y + "" }]));
                            }
                        };
                        canvas.value.onmouseup = (e) => {
                            isDragging.value = false;
                            const { offsetX, offsetY } = e
                            const x = parseInt(offsetX / canvasWidth.value * 100);
                            const y = parseInt(offsetY / canvasHeight.value * 100);
                            socket.value.send(JSON.stringify([{ type: "control", value: "" + beginZuobiao.value[0] + "," + beginZuobiao.value[1] + "," + x + "," + "" + y + "" }]));
                        };
                        canvas.value.onmouseout = (e) => {
                            if (isDragging.value) {
                                isDragging.value = false; // 继续拖动
                            }
                        }
                        canvas.value.onwheel = (e) => {
                            e.preventDefault(); // 阻止默认滚轮行为
                            if (e.deltaY < 0) {
                                // 向上滚动，放大
                                console.log("向上滚动，放大");
                                socket.value.send(JSON.stringify([{ type: "control", value: "50,30,50,70" }]));
                            } else {
                                // 向下滚动，缩小
                                console.log("向下滚动，缩小");
                                socket.value.send(JSON.stringify([{ type: "control", value: "50,70,50,30" }]));
                            }
                        };
                    }
                };
                // setTimeout(() => {
                //     if (!socket.value) return; // 确保 socket 仍然存在
                //     socket.value.send(res)
                // }, 1000);
            } else {
                if (data.control == "close") {
                    // closeLongRange();
                }
            }
        } catch (e) {
            console.error('解析图片数据出错:', e);
        }
    };

    socket.value.onerror = function () {
        clearTimeout(timer); // 错误也清除超时逻辑
        ElNotification({
            title: '错误',
            message: "设备连接失败或不在线",
            type: 'error',
        });
        handleAddLog({
            type: operateType[operationType],
            subdivisionType: subdivideType[subdivide],
            isSuccess: "失败",
            deviceId: props.ids[0].id,
            content: "设备连接失败或不在线"
        });
    };
}
// 添加设备操作指令
function handleAddLog(data){
  addLog(data).then(res => {
    console.log(res);
  }).catch(res => {
    console.log(res);
  });
}
// 关闭远程
function closeLongRange() {
    if (canvas.value && ctx) {
        ctx.clearRect(0, 0, canvas.value.width, canvas.value.height);
    }
    ElNotification({
        title: '提示',
        message: '远程连接已关闭',
        type: 'info',
        position: 'top-left'
    });
    if (!socket.value) return;
    openDrawer.value = false; // 关闭远程对话框
    socket.value.close(); // 关闭 WebSocket 连接
    socket.value = null; // 清空 WebSocket 对象

};
</script>
<style lang="scss" scoped>
.remote-content {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #f5f5f5;
    padding: 10px 0;

    .remote-content-left {
        height: 100%;
        display: flex;
        align-items: flex-start;
    }
}
</style>