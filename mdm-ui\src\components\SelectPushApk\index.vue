<template>

    <el-row>
        <el-col :span="1.5">
            <el-button type="warning" text bg size="large" @click="clickPush('unDownload')" v-hasPerdisa="['group:disabled:push']"
                v-hasPermi="['group:show:push']">一键卸载</el-button>
        </el-col>
        <el-tooltip content="勾选后将替换该组的所有APP" placement="top" effect="light">
            <el-col :span="1.5" style="margin-left: 10px;">
                <el-button type="primary" text bg size="large" @click="clickPush('download')" v-hasPerdisa="['group:disabled:push']"
                    v-hasPermi="['group:show:push']">一键推送</el-button>
            </el-col>
        </el-tooltip>
        <el-col :span="1.5" style="margin-left: 5px;">
            <el-checkbox v-hasPerdisa="['group:disabled:push']" v-hasPermi="['group:show:push']"
                v-model="checkedPackage" label="是否替换设备中同样包名的APP" size="large" />
        </el-col>
    </el-row>

</template>
<script setup>
import { defineProps } from 'vue';
import { ElNotification } from 'element-plus';
import { getGroupApk } from '@/api/group_apk';
import { sendMessageToAndroid } from '@/api/pcUseServerTodevice.js'
const porps = defineProps({
    pushIds: {
        type: Array,
        default: []
    },
});
const data = ref([]);
const checkedPackage = ref(false);
function clickPush(value) {
    data.value = JSON.parse(JSON.stringify(porps.pushIds));
    console.log(data);
    if (data.value.length === 0) {
        ElNotification({
            title: '提示',
            message: '请先选择设备',
            type: 'warning',
            position: 'top-left',
        });
        return;
    }
    return;
    // 获取 data.value 数组中每一项的 id，返回一个数组
    const deviceIds = data.value.map(item => item.id);
    console.log('设备ID数组:', deviceIds);
    sendMessageToAndroid({
        deviceIds,
        type: "server",
        value: value,
        checkedPackage: checkedPackage.value,
        isAllGroup: true
    }).then(res => {
        console.log(res);
        ElNotification({
            title: '提示',
            message: '推送任务已发送',
            type: 'success',
            position: 'top-left',
        });
    });
    return;
    data.value.forEach(device => {
        device.apkList = [];
        const socket = new WebSocket(`ws://${device.ip}:8080`);
        socket.onopen = function () {
            console.log(`${device.ip}已连接`);
            getGroupApk(device.groupId).then(res => {
                console.log(res);
                const thisPushNum = [];
                res.data.forEach(item => {
                    // device.apkList.push(item.apkName);
                    const filePath = item.filePath.split("filePath")[1].replace(/\\/g, "/");
                    device.apkList.push({
                        filePath,
                        packageName: item.packageName,
                        version: item.version
                    });
                    thisPushNum.push(item.fileName);
                });
                console.log(device.apkList);
                socket.send(JSON.stringify([{
                    type: "download",
                    checkedPackage: checkedPackage.value,
                    fileInfoList: device.apkList
                }]));
                addLog({
                    type: "安装指令",
                    subdivisionType: "APK安装",
                    isSuccess: "成功",
                    deviceId: device.id,
                    content: thisPushNum.join(", ") + " 安装指令已成功发送"
                }).then(res => {
                    console.log(res);
                });
            });
        };
        socket.onerror = function (err) {
            console.error(`连接 ${device.ip} 出错`, err);
            ElNotification({
                title: '错误',
                message: `连接 ${device.ip} 失败`,
                type: 'error',
                position: 'top-left',
            });
        };
        device.socket = socket; // 如果你需要保存 socket 引用
    });

    ElNotification({
        title: '提示',
        message: '推送任务已开始',
        type: 'success',
        position: 'top-left',
    });
}

</script>
<style lang="scss" scoped>
:deep(.el-popper.is-customized) {
    /* Set padding to ensure the height is 32px */
    padding: 6px 12px;
    background: linear-gradient(90deg, rgb(159, 229, 151), rgb(204, 229, 129)) !important;
}

:deep(.el-popper.is-customized .el-popper__arrow::before) {
    background: linear-gradient(45deg, #b2e68d, #bce689) !important;
    right: 0;
}
</style>