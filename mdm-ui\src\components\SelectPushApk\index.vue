<template>

    <el-row>
        <el-col :span="1.5">
            <el-button type="warning" text bg size="large" @click="clickPush('unDownload')" v-hasPerdisa="['group:disabled:push']"
                v-hasPermi="['group:show:push']">一键卸载</el-button>
        </el-col>
        <el-tooltip content="勾选后将替换该组的所有APP" placement="top" effect="light">
            <el-col :span="1.5" style="margin-left: 10px;">
                <el-button type="primary" text bg size="large" @click="clickPush('download')" v-hasPerdisa="['group:disabled:push']"
                    v-hasPermi="['group:show:push']">一键推送</el-button>
            </el-col>
        </el-tooltip>
        <el-col :span="1.5" style="margin-left: 5px;">
            <el-checkbox v-hasPerdisa="['group:disabled:push']" v-hasPermi="['group:show:push']"
                v-model="checkedPackage" label="是否替换设备中同样包名的APP" size="large" />
        </el-col>
    </el-row>

</template>
<script setup>
import { defineProps } from 'vue';
import { ElNotification } from 'element-plus';

import { sendMessageToAndroid } from '@/api/pcUseServerTodevice.js'
import { addPushLog } from "@/api/pushLog.js";
const porps = defineProps({
    pushIds: {
        type: Array,
        default: []
    },
});
const data = ref([]);
const checkedPackage = ref(false);
function clickPush(value) {
    data.value = JSON.parse(JSON.stringify(porps.pushIds));
    console.log(data);
    if (data.value.length === 0) {
        ElNotification({
            title: '提示',
            message: '请先选择设备',
            type: 'warning',
            position: 'top-left',
        });
        return;
    }

    // 按分组对设备进行分类
    const groupedDevices = groupDevicesByGroupId(data.value);
    console.log('按分组分类的设备:', groupedDevices);

    // 为每个分组分别调用推送接口
    Object.entries(groupedDevices).forEach(([groupId, devices]) => {
        const deviceIds = devices.map(device => device.id);
        console.log(`分组 ${groupId} 的设备ID数组:`, deviceIds);

        // 调用推送接口
        sendMessageToAndroid({
            deviceIds,
            type: "server",
            value: value,
            checkedPackage: checkedPackage.value,
            isAllGroup: true
        }).then(res => {
            console.log(`分组 ${groupId} 推送结果:`, res);
        });

        // 为每个分组调用 addPushLog 接口
        addPushLog({
            deviceIds: deviceIds.join(","),
            groupId: groupId,
            apkIds: ""
        }).then(res => {
            console.log(`分组 ${groupId} 推送日志记录结果:`, res);
        });
    });

    // 显示成功通知
    ElNotification({
        title: '提示',
        message: '推送任务已发送',
        type: 'success',
        position: 'top-left',
    });
}

// 按分组ID对设备进行分类的函数
function groupDevicesByGroupId(devices) {
    const grouped = {};
    devices.forEach(device => {
        const groupId = device.groupId;
        if (!grouped[groupId]) {
            grouped[groupId] = [];
        }
        grouped[groupId].push(device);
    });
    return grouped;
}

</script>
<style lang="scss" scoped>
:deep(.el-popper.is-customized) {
    /* Set padding to ensure the height is 32px */
    padding: 6px 12px;
    background: linear-gradient(90deg, rgb(159, 229, 151), rgb(204, 229, 129)) !important;
}

:deep(.el-popper.is-customized .el-popper__arrow::before) {
    background: linear-gradient(45deg, #b2e68d, #bce689) !important;
    right: 0;
}
</style>