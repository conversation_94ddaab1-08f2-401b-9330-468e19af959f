<template>
    <el-button type="primary" text :underline="false" @click="handleOpen()" size="small"
        v-hasPerdisa="['group:disabled:installApk']" v-hasPermi="['group:show:installApk']"
        :disabled="row.status == 2">安装APK</el-button>
    <el-drawer v-model="open" title="设备已实现&组策略" size="100%" direction="rtl" @close="handleClose" append-to-body>
        <div class="remote-content">
            <!-- <div class="align-items: flex-start;">
                <el-checkbox v-model="checkedPackage" label="是否替换设备中同样包名的APP" size="large" />
            </div> -->
            <el-transfer :props="prop" v-model="value" :data="data" :titles="['组策略', '当前设备已安装APK']"
                :button-texts="['移除', '推送']" @change="handleChange">
                <template #default="{ option }">
                    <div class="item">
                        <div style="display: flex; justify-content: center; align-items: center; padding: 0 10px;">
                            <img :src="`http://${option.apkImg}`" alt="" style="width: 28px; height: 28px;"
                                @error="e => e.target.src = defaultImg" />
                        </div>
                        <div><span>{{ option.apkName }} - {{ option.packageName }}</span></div>
                    </div>
                </template>
                <template #left-empty>
                    <el-empty :image-size="60" description="No data" />
                </template>
                <template #right-empty>
                    <el-empty :image-size="60" description="No data" />
                </template>
            </el-transfer>
        </div>
    </el-drawer>
</template>
<script setup>
import { addLog } from "@/api/log/operate.js";
import { addPushLog } from "@/api/pushLog.js";
import { sendMessageToAndroid } from '@/api/pcUseServerTodevice.js'
import { defineProps } from 'vue'
import { getGroupApk, addGroupApk } from '@/api/group_apk'
import { ElNotification } from 'element-plus'
import defaultImg from '@/assets/images/wenjian.png'
const props = defineProps({
    row: {
        type: Object,
        default: () => ({})
    }
})
const prop = {
    key: 'apkId',
    label: 'apkName',
    disabled: false,
}
const open = ref(false)
const data = ref([])
const value = ref([]) // 
const groupId = ref("")
const socket = ref(null)
const checkedPackage = ref(true)
const checkedVersion = ref(true)

const mobileApkList = ref([]) // 设备APK列表
const apkList = ref([]) // 当前设备已安装的APK列表

function handleOpen() {
    groupId.value = props.row.groupId;
    value.value = [];

    const timeout = 5000; // 设定连接超时时间（毫秒）
    let didConnect = false;

    getGroupApk(groupId.value).then((res) => {
        data.value = res.data;
        console.log(data.value);
        socket.value = new WebSocket(`ws://${props.row.ip}:8080`);
        // 设置超时处理逻辑
        const timer = setTimeout(() => {
            if (!didConnect) {
                socket.value.close();
                ElNotification({
                    title: '连接超时',
                    message: '设备连接超时，请检查网络或设备状态',
                    type: 'error',
                    position: 'top-left',
                });
            }
        }, timeout);
        socket.value.onopen = function () {
            didConnect = true;
            clearTimeout(timer); // 连接成功，清除超时定时器
            console.log("WebSocket 已连接");
            open.value = true;
            // 发送获取 APK 列表的请求
            // getApkDataList();
            socket.value.send(JSON.stringify([{
                type: "get",
                value: "apkListDetailNoLocal"
            }]));
        };
        socket.value.onmessage = function (event) {
            const res = JSON.parse(event.data);
            if (res.type === "apkListDetailNoLocal") {
                mobileApkList.value = res.appList;
                console.log("设备 APK 列表:", res.appList);
                for (let i = 0; i < data.value.length; i++) {
                    for (let j = 0; j < mobileApkList.value.length; j++) {
                        // 情况1：匹配APK应用（通过包名和版本）
                        if (mobileApkList.value[j].packageName && mobileApkList.value[j].versionName) {
                            if (data.value[i].packageName === mobileApkList.value[j].packageName &&
                                data.value[i].version === mobileApkList.value[j].versionName) {
                                value.value.push(data.value[i].apkId);
                                found = true;
                                break;
                            }
                        }

                        // 情况2：匹配文件（通过文件名）
                        if (!found && mobileApkList.value[j].fileName) {
                            if (data.value[i].apkName === mobileApkList.value[j].fileName) {
                                value.value.push(data.value[i].apkId);
                                found = true;
                                break;
                            }
                        }
                        // if ((data.value[i].packageName === mobileApkList.value[j].packageName && data.value[i].version === mobileApkList.value[j].versionName) || data.value[i].apkName === mobileApkList.value[j].fileName) {
                        //     value.value.push(data.value[i].apkId);
                        //     break;
                        // }
                    }
                }
                console.log(value.value);
                open.value = true;
                apkList.value = JSON.parse(JSON.stringify(value.value));
                // socket.value.close();
            }
        };
        socket.value.onerror = function () {
            clearTimeout(timer); // 错误也清除超时逻辑
            ElNotification({
                title: '错误',
                message: "设备连接失败或不在线",
                type: 'error',
            });
            addLog({
                type: "安装指令",
                subdivisionType: "安装指令",
                isSuccess: "成功",
                deviceId: props.row.id,
                content: "安装或卸载指令发送失败，设备不在线或设备关闭或设备一关闭wifi或设备ip错误"
            }).then(res => {
                console.log(res);
            });
            socket.value.close();
        };
    });
}

function handleChange() {
    if (apkList.value.length < value.value.length) {
        // 说明是添加
        const thisPushId = [] // 要安装APK的id
        const thisPushPackage = []
        const thisPushNum = []
        value.value.forEach(item => {
            if (!apkList.value.includes(item)) {
                thisPushId.push(item)
            }
        });
        console.log(thisPushId);
        sendMessageToAndroid({
            deviceIds: [props.row.id],
            type: "server",
            value: "download",
            checkedPackage: true,
            apkIds: thisPushId,
            isAllGroup: false
        }).then(res => {
        });
        // 发送安装指令
        addPushLog({
            deviceIds: props.row.id,
            apkIds: thisPushId.join(",")
        }).then(res => {
            console.log(res);
        });
        return;
        data.value.forEach((item) => {
            if (thisPushId.includes(item.apkId)) {
                thisPushNum.push(item.apkName)
                thisPushPackage.push({ filePath: item.filePath.split("filePath")[1].replace(/\\/g, "/") });
            }
        });
        console.log(thisPushPackage);
        socket.value.send(JSON.stringify([{
            type: "download",
            // value: "download",
            checkedPackage: true,
            // valueList: thisPushPackage,
            fileInfoList: thisPushPackage
        }]));
        addLog({
            type: "安装指令",
            subdivisionType: "APK安装",
            isSuccess: "成功",
            deviceId: props.row.id,
            content: thisPushNum.join(", ") + " 安装指令已成功发送"
        }).then(res => {
            console.log(res);
        });
    } else {
        // 说明是删除
        const thisRemoveId = []
        const thisRemovePackage = []
        apkList.value.forEach(item => {
            if (!value.value.includes(item)) {
                thisRemoveId.push(item)
            }
        });
        sendMessageToAndroid({
            deviceIds: [props.row.id],
            type: "server",
            value: "unDownload",
            checkedPackage: true,
            apkIds: thisRemoveId,
            isAllGroup: false
        }).then(res => {
            console.log(res);
        });
        return;
        data.value.forEach((item) => {
            if (thisRemoveId.includes(item.apkId)) {
                thisRemovePackage.push(item.packageName)
            }
        });
        console.log(thisRemovePackage);
        socket.value.send(JSON.stringify([{
            type: "unDownload",
            valueList: thisRemovePackage
        }]));
        addLog({
            type: "卸载指令",
            subdivisionType: "APK卸载",
            isSuccess: "成功",
            deviceId: props.row.id,
            content: thisRemovePackage.join(", ") + " 卸载指令已成功发送"
        }).then(res => {
            console.log(res);
        });
    }
    apkList.value = JSON.parse(JSON.stringify(value.value));
}

function getList() {
    getGroupApk(groupId.value).then((res) => {
        console.log(res.data);
        data.value = res.data
    });
}
function handleClose() {
    open.value = false;
    if (socket.value) {
        socket.value.close();
    }
}
</script>
<style lang="scss" scoped>
.remote-content {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background-color: #f5f5f5;

    .item {
        width: 100%;
        height: 100%;
        display: flex;
    }
}

:deep(.el-transfer-panel) {
    width: 500px !important;
    height: 500px !important;
}

:deep(.el-transfer-panel__body) {
    height: calc(100% - 40px) !important;
}

:deep(.el-transfer-panel__list) {
    height: 100% !important;
}
</style>