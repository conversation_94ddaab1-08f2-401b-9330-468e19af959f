<template>
  <div class="login-content">
    <div class="login-header">
      <!-- <img src="../assets/logo/logo.png" alt="logo" style="height: 64px;" /> -->
      <div class="title">IoT数字化管理平台</div>
      <div style="flex: 1;"></div>
      <div class="right">
        <svg-icon icon-class="earth" style="font-size: 32px;margin-right: 5px;"/>
        <el-dropdown size="large" placement="bottom-end">
          <span style="color: #fff;font-size: 15px;">
            简体中文
          </span>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item>简体中文</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>
    <div class="login">
      <el-form ref="loginRef" :model="loginForm" :rules="loginRules" class="login-form">
        <h3 class="title">IOT数字化管理平台</h3>
        <el-form-item prop="username">
          <el-input v-model="loginForm.username" type="text" size="large" auto-complete="off" placeholder="账号">
            <template #prefix><svg-icon icon-class="user" class="el-input__icon input-icon" /></template>
          </el-input>
        </el-form-item>
        <el-form-item prop="password">
          <el-input v-model="loginForm.password" type="password" size="large" auto-complete="off" placeholder="密码"
            @keyup.enter="handleLogin">
            <template #prefix><svg-icon icon-class="password" class="el-input__icon input-icon" /></template>
          </el-input>
        </el-form-item>
        <el-checkbox v-model="loginForm.rememberMe" style="margin:0px 0px 25px 0px;color: #ccc;">记住密码</el-checkbox>
        <el-form-item style="width:100%;">
          <el-button :loading="loading" size="large" type="primary" style="width:100%;" @click.prevent="handleLogin">
            <span v-if="!loading">登 录</span>
            <span v-else>登 录 中...</span>
          </el-button>
          <div style="float: right;" v-if="register">
            <router-link class="link-type" :to="'/register'">立即注册</router-link>
          </div>
        </el-form-item>
      </el-form>
      <!--  底部  -->
      <div class="el-login-footer">
      </div>
    </div>
  </div>
</template>

<script setup>
import Cookies from "js-cookie";
import { encrypt, decrypt } from "@/utils/jsencrypt";
import useUserStore from '@/store/modules/user'
import { h } from 'vue'
import { ElNotification } from 'element-plus'

const userStore = useUserStore()
const route = useRoute();
const router = useRouter();
const { proxy } = getCurrentInstance();

const loginForm = ref({
  username: "admin",
  password: "admin123",
  rememberMe: false,
  uuid: ""
});

const loginRules = {
  username: [{ required: true, trigger: "blur", message: "请输入您的账号" }],
  password: [{ required: true, trigger: "blur", message: "请输入您的密码" }]
};

const codeUrl = ref("");
const loading = ref(false);
// 注册开关
const register = ref(false);
const redirect = ref(undefined);

watch(route, (newRoute) => {
  redirect.value = newRoute.query && newRoute.query.redirect;
}, { immediate: true });

function handleLogin() {
  proxy.$refs.loginRef.validate(valid => {
    if (valid) {
      loading.value = true;
      // 勾选了需要记住密码设置在 cookie 中设置记住用户名和密码
      if (loginForm.value.rememberMe) {
        Cookies.set("username", loginForm.value.username, { expires: 30 });
        Cookies.set("password", encrypt(loginForm.value.password), { expires: 30 });
        Cookies.set("rememberMe", loginForm.value.rememberMe, { expires: 30 });
      } else {
        // 否则移除
        Cookies.remove("username");
        Cookies.remove("password");
        Cookies.remove("rememberMe");
      }
      // 调用action的登录方法
      userStore.login(loginForm.value).then(() => {
        const query = route.query;
        const otherQueryParams = Object.keys(query).reduce((acc, cur) => {
          if (cur !== "redirect") {
            acc[cur] = query[cur];
          }
          return acc;
        }, {});
        router.push({ path: redirect.value || "/", query: otherQueryParams });
      }).catch(() => {
        loading.value = false;
      });
      // 检验license
      userStore.checkLicense().then((res) => {
        console.log(res);
        // 过滤出即将过期的许可证
        const soonExpire = res.data.filter(element => (element.day < 30) && (element.day >= 0));
        if (soonExpire.length > 0) {
          ElNotification({
            title: 'License过期提醒',
            dangerouslyUseHTMLString: true,
            message: soonExpire.map(element =>
              `<div style="color: teal;">有${element.deviceCount || ''}个许可证即将过期，还剩${element.day}天</div>`
            ).join(''),
            duration: 0 // 不自动关闭
          });
        }
      }).catch(() => {
      });
    }
  });
}


function getCookie() {
  const username = Cookies.get("username");
  const password = Cookies.get("password");
  const rememberMe = Cookies.get("rememberMe");
  loginForm.value = {
    username: username === undefined ? loginForm.value.username : username,
    password: password === undefined ? loginForm.value.password : decrypt(password),
    rememberMe: rememberMe === undefined ? false : Boolean(rememberMe)
  };
}

getCookie();
</script>

<style lang='scss' scoped>
.login-content {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;

  .login-header {
    width: 100%;
    height: 64px;
    background-color: #333744;
    display: flex;

    .title {
      font-size: 22px;
      color: #dbe0eb;
      margin-left: 18px;
      // margin-left: 8px;
      line-height: 64px;
      cursor: pointer;
      font-weight: 500;
      font-family: Helvetica Neue Light, Helvetica Neue, Helvetica, Microsoft Yahei, WenQuanYi Micro Hei, Lucida Grande, sans-serif;
    }

    .right {
      margin-left: auto;
      margin-right: 16px;
      line-height: 64px;
      cursor: pointer;
      color: #dbe0eb;
      display: flex;
      align-items: center;


    }
  }
}

.login {
  position: relative;
  display: flex;
  // justify-content: center;
  align-items: center;
  height: 100%;
  // background-image: url("../assets/images/login-background.jpg");
  // background-size: cover;

}

.login::before {
  content: '';
  position: absolute;
  z-index: -1;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  background-image: url("../assets/images/login_bg.jpg");
  background-size: cover;
  filter: blur(0px);
}

.title {
  margin: 0px auto 30px auto;
  text-align: center;
  color: rgb(255, 255, 255);
  // color: rgb(206, 204, 204);
  // color: rgb(47, 47, 47);
  font-weight: bold;
  font-size: 18px;
  position: relative;
}

.login-form {
  border-radius: 6px;
  background: transparent;
  width: 400px;
  // padding: 25px 25px 5px 25px;
  padding: 32px;
  position: relative;
  left: calc(100vw - 150px - 400px);

  .el-input {
    height: 40px;

    input {
      height: 40px;
    }
  }

  .input-icon {
    height: 39px;
    width: 14px;
    margin-left: 0px;
  }
}

.login-form::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba($color: #ffffff, $alpha: .1);
  filter: blur(1px);
  transition: background-color 0.3s ease, filter 0.3s ease;
}

.login-form:hover::before {
  background-color: rgba($color: #ffffff, $alpha: .3);
  filter: blur(1px);
}

.login-tip {
  font-size: 13px;
  text-align: center;
  color: #bfbfbf;
}

.login-code {
  width: 33%;
  height: 40px;
  float: right;

  img {
    cursor: pointer;
    vertical-align: middle;
  }
}

.el-login-footer {
  height: 40px;
  line-height: 40px;
  position: fixed;
  bottom: 0;
  width: 100%;
  text-align: center;
  color: #fff;
  font-family: Arial;
  font-size: 12px;
  letter-spacing: 1px;
}

.login-code-img {
  height: 40px;
  padding-left: 12px;
}
</style>
