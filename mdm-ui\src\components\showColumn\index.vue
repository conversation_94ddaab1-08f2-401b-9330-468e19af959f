<template>
    <div class="showColumn">
        <el-dropdown trigger="click" :hide-on-click="false" style="padding-left: 12px" placement="bottom-end" max-height="500">
            <el-button text bg size="large" icon="Tools" style="font-size: 20px;color: black;"></el-button>
            <template #dropdown>
                <el-dropdown-menu>
                    <template v-for="item in columns" :key="item.key">
                        <el-dropdown-item>
                            <el-checkbox :checked="item.visible" @change="checkboxChange($event, item.label)" :label="item.label" />
                        </el-dropdown-item>
                    </template>
                </el-dropdown-menu>
            </template>
        </el-dropdown>
    </div>
</template>
<script setup>
const props = defineProps({
    /* 显隐列信息 */
    columns: {
        type: Array,
    },
})
// 勾选
function checkboxChange(event, label) {
  props.columns.filter(item => item.label == label)[0].visible = event;
}
</script>
<style lang="scss" scoped>
.showColumn {
    .el-button {
        padding: 12px !important;
    }

}
:deep(.el-dropdown-menu__item) {
    padding: 0 16px !important;
}
</style>