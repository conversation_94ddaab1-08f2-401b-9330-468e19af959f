<template>
    <el-link type="primary" @click.prevent="handleOpen">{{ row.model }}</el-link>
    <el-drawer v-model="open" :show-close="false" append-to-body :with-header="false" size="90%" @close="handleClose">
        <div class="content">
            <div class="content_header">
                <div class="content_header_left">
                    <div class="content_header_left_header">
                        {{ row.model }}
                    </div>
                    <div class="content_header_left_footer">
                        <span v-if="row.status == 1"
                            style="display:inline-block; width: 10px;height: 10px;border-radius: 50%; background-color: #00ff00;padding-top: 5px;line-height: 24px;"></span>
                        <span v-else
                            style="display:inline-block; width: 10px;height: 10px;border-radius: 50%; background-color: #f0f0f0;padding-top: 5px;line-height: 24px;"></span>
                        <span>状态：</span><span>{{ row.status == 1 ? '在线' : '离线'
                        }}</span>&nbsp;&nbsp;&nbsp;&nbsp;丨&nbsp;&nbsp;&nbsp;&nbsp;
                        <span>注册时间：</span><span>{{ row.createTime
                        }}</span>&nbsp;&nbsp;&nbsp;&nbsp;丨&nbsp;&nbsp;&nbsp;&nbsp;
                        <span>上次心跳时间：</span><span>{{ row.heartbeatTime
                        }}</span>&nbsp;&nbsp;&nbsp;&nbsp;丨&nbsp;&nbsp;&nbsp;&nbsp;
                        <span>所在组：</span><span>{{ row.groupName }}</span>
                    </div>
                </div>
                <div class="content_header_right">
                    <el-button type="plain" text @click="handleClose" size="small">关闭</el-button>
                </div>
            </div>
            <div class="content_footer">
                <el-tabs v-model="activeName" type="border-card" class="demo-tabs" @tab-click="handleClick">
                    <el-tab-pane label="常用信息" name="常用信息">
                        <div class="custom-tabs-content">
                            <div class="custom-tabs-content-1">
                                <el-card shadow="hover">
                                    <template #header>
                                        <div class="card-header">
                                            <span>基本信息</span>
                                        </div>
                                    </template>
                                    <form label-width="68px">
                                        <div class="fomr_item"><el-form-item label="型号:">{{ row.model }}</el-form-item>
                                        </div>
                                        <div class="fomr_item"><el-form-item label="IMEI:">{{ row.IMEI || '-/-'
                                                }}</el-form-item></div>
                                        <div class="fomr_item"><el-form-item label="MEID:">{{ row.MEID || '-/-'
                                                }}</el-form-item></div>
                                        <div class="fomr_item"><el-form-item label="资产编号:">{{ row.equipment || '-/-'
                                                }}</el-form-item></div>
                                        <div class="fomr_item"><el-form-item label="备注:">{{ row.remark || '-/-'
                                                }}</el-form-item></div>
                                    </form>
                                </el-card>
                                <el-card shadow="hover">
                                    <template #header>
                                        <div class="card-header">
                                            <span>硬件信息</span>
                                        </div>
                                    </template>
                                    <form label-width="68px">
                                        <div class="fomr_item"><el-form-item label="操作系统:">{{ row.platform || '-/-'
                                                }}</el-form-item></div>
                                        <div class="fomr_item"><el-form-item label="系统版本:">{{ row.version || '-/-'
                                                }}</el-form-item></div>
                                        <div class="fomr_item"><el-form-item label="序列号:">{{ row.sn || '-/-'
                                                }}</el-form-item></div>
                                        <div class="fomr_item"><el-form-item label="电池电量:">{{ row.battery || '-/-'
                                                }}</el-form-item></div>
                                        <div class="fomr_item"><el-form-item label="是否充电中:">{{ row.isRecharge || '-/-'
                                                }}</el-form-item></div>
                                        <div class="fomr_item"><el-form-item label="今日活跃时长">{{ row.activityTime *
                                            0.5 + '分钟' || '-/-' }}</el-form-item>
                                        </div>
                                    </form>
                                </el-card>
                                <el-card shadow="hover">
                                    <template #header>
                                        <div class="card-header">
                                            <span>网络信息</span>
                                        </div>
                                    </template>
                                    <form label-width="68px">
                                        <div class="fomr_item"><el-form-item label="IP:">{{ row.ip || '-/-'
                                                }}</el-form-item></div>
                                        <div class="fomr_item"><el-form-item label="MAC:">{{ row.mac || '-/-'
                                                }}</el-form-item></div>
                                        <div class="fomr_item"><el-form-item label="WIFI名称:">{{ row.wifiName || '-/-'
                                                }}</el-form-item></div>
                                        <div class="fomr_item"><el-form-item label="WIFI信号值:">{{ row.rssi || '-/-'
                                                }}</el-form-item></div>
                                        <div class="fomr_item"><el-form-item label="AP_MAC:">{{ row.apMac || '-/-'
                                                }}</el-form-item></div>
                                    </form>
                                </el-card>
                            </div>
                        </div>
                    </el-tab-pane>
                    <el-tab-pane label="已安装应用" name="已安装应用">
                        <div class="custom-tabs-content">
                            <div class="custom-tabs-content-2">
                                <el-table size="small" border v-loading="loadingYet" class="table-height" :data="apkDataNoLocal">
                                    <el-table-column label="序号" type="index" width="50" align="center"></el-table-column>
                                    <el-table-column label="程序名" prop="appName" show-overflow-tooltip></el-table-column>
                                    <el-table-column label="包名" prop="packageName" show-overflow-tooltip></el-table-column>
                                    <el-table-column label="版本" prop="versionName" show-overflow-tooltip sortable></el-table-column>
                                    <el-table-column label="安装时间" prop="firstInstallTime" show-overflow-tooltip sortable>
                                        <template #default="scope">
                                            <div>{{ new Date(scope.row.firstInstallTime).toLocaleString() }}</div>
                                        </template>
                                    </el-table-column>
                                </el-table>
                            </div>
                        </div>
                    </el-tab-pane>
                    <el-tab-pane label="设备自带应用" name="设备自带应用">
                        <div class="custom-tabs-content">
                            <div class="custom-tabs-content-2">
                                <el-table size="small" border v-loading="loadingLocal" class="table-height" :data="apkDataLocal">
                                    <el-table-column label="序号" type="index" width="50" align="center"></el-table-column>
                                    <el-table-column label="程序名" prop="appName" show-overflow-tooltip></el-table-column>
                                    <el-table-column label="包名" prop="packageName" show-overflow-tooltip></el-table-column>
                                    <el-table-column label="版本" prop="versionName" show-overflow-tooltip sortable></el-table-column>
                                    <el-table-column label="安装时间" prop="firstInstallTime" show-overflow-tooltip sortable>
                                        <template #default="scope">
                                            <div>{{ new Date(scope.row.firstInstallTime).toLocaleString() }}</div>
                                        </template>
                                    </el-table-column>
                                </el-table>
                            </div>
                        </div>
                    </el-tab-pane>
                    <el-tab-pane label="设备操作日志" name="设备操作日志">
                        <div class="custom-tabs-content">
                            <div class="custom-tabs-content-3">
                                <el-table :data="deviceOperationLogList" stripe class="table-height" size="small" border>
                                    <el-table-column label="序号" width="55" type="index" align="center"/>
                                    <el-table-column label="操作类型" align="center" prop="type" width="120"
                                        show-overflow-tooltip sortable />
                                    <el-table-column label="日志细分" align="center" prop="subdivisionType" width="120"
                                        show-overflow-tooltip />
                                    <el-table-column label="是否成功" align="center" prop="isSuccess" width="110"
                                        show-overflow-tooltip sortable>
                                    </el-table-column>
                                    <el-table-column label="MAC" align="center" prop="mac" width="150"
                                        show-overflow-tooltip />
                                    <el-table-column label="分组" align="center" prop="groupName" width="100"
                                        show-overflow-tooltip sortable />
                                    <el-table-column label="IP" align="center" prop="ip" width="130"
                                        show-overflow-tooltip sortable />
                                    <el-table-column label="设备名称" align="center" prop="deviceName" width="150"
                                        show-overflow-tooltip />
                                    <el-table-column label="操作时间" align="center" prop="createTime" width="180"
                                        show-overflow-tooltip sortable fixed="right" />
                                    <el-table-column label="日志内容" align="center" prop="content" show-overflow-tooltip
                                        width="200" />
                                </el-table>
                                <pagination v-show="total >= 0" :total="total" v-model:page="params.pageNum"
                                    v-model:limit="params.pageSize" @pagination="getdeviceOperationLogList" />
                            </div>
                        </div>
                    </el-tab-pane>
                </el-tabs>
            </div>
        </div>
    </el-drawer>
</template>
<script setup>
import { ref } from 'vue';
import { defineProps } from 'vue';
import { getLogByDeviceId } from '@/api/log/operate';
import { ElNotification } from 'element-plus';
const props = defineProps({
    row: {
        type: Object,
        default: () => ({})
    }
});
const open = ref(false);
const apkDataNoLocal = ref([]);
const apkDataLocal = ref([]);
const loadingYet = ref(false); // 设备已安装
const loadingLocal = ref(false); // 设备自带

const activeName = ref('常用信息');
const webSocket = ref(null);
const deviceOperationLogList = ref([]); // 设备操作日志
const total = ref(0);
const params = {
    pageSize: 20,
    pageNum: 1
}
function getdeviceOperationLogList() {
    getLogByDeviceId({ deviceId: props.row.id, ...params }).then(res => {
        console.log(res);
        total.value = res.total;
        deviceOperationLogList.value = res.rows;
    })
}
function handleOpen() {
    open.value = true;
}
function handleClose() {
    open.value = false;
}
function handleClick(tab, event) {
    console.log(tab.paneName);
    if (tab.paneName == '已安装应用') {
        loadingYet.value = true;
        if (apkDataNoLocal.value.length != 0) {
            loadingYet.value = false;
            return
        }
        if (webSocket.value) {
            webSocket.value.close();
            webSocket.value = null;
        }
        webSocket.value = new WebSocket(`ws://${props.row.ip}:8080/`);
        webSocket.value.onopen = function () {
            webSocket.value.send(JSON.stringify([{
                type: "get",
                value: "apkListDetailNoLocal"
            }]))
        }
        webSocket.value.onmessage = function (event) {
            const res = JSON.parse(event.data);
            console.log(res);
            if (res.type === "apkListDetailNoLocal") {
                apkDataNoLocal.value = res.appList;
            }
            loadingYet.value = false;
        }
        webSocket.value.onerror = function () {
            ElNotification({
                title: '错误',
                message: "设备连接失败或不在线",
                type: 'error',
            });
            webSocket.value.close();
        };
    } else if (tab.paneName == '设备自带应用') {
        loadingLocal.value = true;
        if (apkDataLocal.value.length != 0) {
            loadingLocal.value = false;
            return
        }
        if (webSocket.value) {
            webSocket.value.close();
            webSocket.value = null;
        }
        webSocket.value = new WebSocket(`ws://${props.row.ip}:8080/`);
        webSocket.value.onopen = function () {
            webSocket.value.send(JSON.stringify([{
                type: "get",
                value: "apkListDetailLocal"
            }]))
        }
        webSocket.value.onmessage = function (event) {
            const res = JSON.parse(event.data);
            console.log(res);
            if (res.type === "apkListDetailLocal") {
                apkDataLocal.value = res.appList;
            }
            loadingLocal.value = false;
        }
        webSocket.value.onerror = function () {
            ElNotification({
                title: '错误',
                message: "设备连接失败或不在线",
                type: 'error',
            });
            webSocket.value.close();
        };
    } else if (tab.paneName == '设备操作日志') {
        if (deviceOperationLogList.value.length == 0) {
            getdeviceOperationLogList()
        }
    }
}
</script>
<style lang="scss" scoped>
.content {
    height: 100%;
    display: flex;
    flex-direction: column;

    .content_header {
        height: 64px;
        display: flex;
        margin-bottom: 10px;

        .content_header_left {
            flex: 1;
            display: flex;
            flex-direction: column;

            .content_header_left_header {
                height: 40px;
                font-size: 20px;
                line-height: 48px;
                font-weight: bold;
                padding-left: 10px;
            }

            .content_header_left_footer {
                flex: 1;
                font-size: 12px;
                padding-left: 10px;
                font-family: Microsoft Yahei, sans-serif
            }
        }

        .content_header_right {
            width: 64px;
            display: flex;
            justify-content: flex-end;
            display: flex;
        }
    }

    .content_footer {
        flex: 1;


    }
}

:deep(.el-tabs__content) {
    width: 100%;
    height: calc(100% - 39px);
    padding: 0 !important;

    .el-tab-pane {
        height: 100%;
    }
}

:deep(.demo-tabs) {
    width: 100%;
    height: 100%;
}

.custom-tabs-content {
    width: 100%;
    height: 100%;
}

.custom-tabs-content-1 {
    width: 100%;
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 16px;
    padding: 10px;
    align-items: start;

    .fomr_item {
        width: 100%;
        border-bottom: 2px solid #ebeef5;
        margin-bottom: 10px;
    }
}

:deep(.el-form-item) {
    margin-bottom: 0px !important;
}

.custom-tabs-content-2 {
    width: 100%;
    padding: 10px;

    .table-height {
        height: calc(100vh - 200px) !important;
    }

}

.custom-tabs-content-3 {
    width: 100%;
    padding: 10px;
    overflow: hidden;

    .table-height {
        height: calc(100vh - 220px) !important;
    }

}
</style>