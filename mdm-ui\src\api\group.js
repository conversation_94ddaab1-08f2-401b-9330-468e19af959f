import request from '@/utils/request'

// 查询分组列表
export function listGroup(query) {
  return request({
    url: '/pc/group/list',
    method: 'get',
    params: query
  })
}

// 查询分组详细
export function getGroup(id) {
  return request({
    url: '/pc/group/' + id,
    method: 'get'
  })
}

// 新增分组
export function addGroup(data) {
  return request({
    url: '/pc/group',
    method: 'post',
    data: data
  })
}

// 修改分组
export function updateGroup(data) {
  return request({
    url: '/pc/group',
    method: 'put',
    data: data
  })
}

// 删除分组
export function delGroup(id) {
  return request({
    url: '/pc/group/' + id,
    method: 'delete'
  })
}

// 查询设备列表
export function listDevice(query) {
  return request({
    url: '/device/device/list',
    method: 'get',
    params: query
  })
}
// 查询设备详细
export function getDevice(id) {
  return request({
    url: '/device/device/' + id,
    method: 'get'
  })
}

// 新增设备
export function addDevice(data) {
  return request({
    url: '/device/device',
    method: 'post',
    data: data
  })
}

// 修改设备
export function updateDevice(data) {
  return request({
    url: '/device/device',
    method: 'put',
    data: data
  })
}

// 删除设备
export function delDevice(id) {
  return request({
    url: '/device/device/' + id,
    method: 'delete'
  })
}

// 修改设备组
export function updateDeviceGroup(data) {
  return request({
    url: '/pc/group/updateGroup',
    method: 'post',
    data: data
  })
}
