// base color
$blue: #324157;
$light-blue: #3A71A8;
$red: #C03639;
$pink: #E65D6E;
$green: #30B08F;
$tiffany: #4AB7BD;
$yellow: #FEC171;
$panGreen: #30B08F;

// 默认菜单主题风格
// $base-menu-color: #bfcbd9;
// $base-menu-color-active: #f4f4f5;
// $base-menu-background: #304156;
// $base-logo-title-color: #ffffff;

// $base-menu-light-color: rgba(0, 0, 0, 0.7);
// $base-menu-light-background: #ffffff;
// $base-logo-light-title-color: #001529;

// $base-sub-menu-background: #1f2d3d;
// $base-sub-menu-hover: #001528;

// 自定义暗色菜单风格

// $base-menu-color:hsla(0,0%,100%,.65);
// $base-menu-color-active:#fff;
// $base-menu-background:#001529;
// $base-logo-title-color: #ffffff;

// $base-menu-light-color:rgba(0,0,0,.70);
// $base-menu-light-background:#ffffff;
// $base-logo-light-title-color: #001529;

// $base-sub-menu-background:#000c17;
// $base-sub-menu-hover:#001528;

// 自定义紫色菜单风格
// $base-menu-color: rgba(255, 255, 255);
// $base-menu-color-active: rgb(255,255,255);
// $base-menu-background-active: #4B0082;
// $base-menu-background: #722ed1;
// $base-logo-title-color: #F8F0FC;

// $base-menu-light-color: rgba(245, 241, 241, 1);
// $base-menu-light-background: #605ca8;
// $base-logo-light-title-color: #4B0082;

// $base-sub-menu-background: #8f52e5;
// $base-sub-menu-hover: #4B0082;

// 重写紫色风格
// $base-menu-color: #777; // 菜单文字颜色
// $base-menu-color-active: #722ed1; // 菜单选中颜色
// $base-menu-background: #fff; // 菜单背景颜色
// $base-logo-background: #555299; // logo背景颜色
// $base-navbar-background: #6a66b9; // 顶部导航栏背景颜色
// $base-menu-background-active: #f9f0ff; // 菜单选中背景颜色
// $base-logo-title-color: #F8F0FC; // logo标题颜色
// $base-menu-light-color: #777; // 浅色菜单文字颜色
// $base-menu-light-background: #fff; // 浅色菜单背景颜色
// $base-logo-light-title-color: #555299; // 浅色logo标题颜色
// $base-sub-menu-background: #fff; // 子菜单背景颜色
// $base-sub-menu-hover: #f9f0ff; // 子菜单悬停颜色

// 商务风格
// $base-menu-color: #4a4a4a;                // 菜单文字颜色：深灰，沉稳易读
// $base-menu-color-active: #1e87f0;         // 菜单选中颜色：商务蓝，专业且醒目
// $base-menu-background: #f5f7fa;           // 菜单背景颜色：浅灰蓝调，低调柔和
// $base-logo-background: #2b2f3a;           // logo背景颜色：深灰蓝，稳重专业
// $base-navbar-background: #1f232e;         // 顶部导航栏背景：深色，拉高视觉层级
// $base-menu-background-active: #e6f4ff;    // 菜单选中背景：浅蓝，清晰但不突兀
// $base-logo-title-color: #ffffff;          // logo标题颜色：白色，搭配深背景更专业
// $base-menu-light-color: #4a4a4a;          // 浅色菜单文字颜色
// $base-menu-light-background: #ffffff;     // 浅色菜单背景
// $base-logo-light-title-color: #1e1e2f;    // 浅色logo标题颜色：深蓝灰
// $base-sub-menu-background: #f9fafc;       // 子菜单背景颜色：高灰度白
// $base-sub-menu-hover: #dbefff;            // 子菜单悬停颜色：淡蓝，体现交互感

// 商务风格2
// $base-menu-color: #4E4E4E;               // 菜单文字：优雅深灰，沉稳清晰
// $base-menu-color-active: #A582FF;        // 菜单选中：高贵紫金调，点缀而不浮夸
// $base-menu-background: #F9F9FB;          // 菜单背景：高级灰白，干净有质感
// $base-logo-background: #2C2C3A;          // logo背景：藏蓝黑，内敛中显质感
// $base-navbar-background: #1F1F2E;        // 顶部导航栏背景：深灰紫调，层级分明
// $base-menu-background-active: #EFE9FF;   // 菜单选中背景：淡雅紫灰，清雅精致
// $base-logo-title-color: #F8F6F9;         // logo标题颜色：柔白，提升品质感
// $base-menu-light-color: #3D3D3D;         // 浅色菜单文字颜色
// $base-menu-light-background: #FFFFFF;    // 浅色菜单背景
// $base-logo-light-title-color: #4E406B;   // 浅色logo标题：紫灰调
// $base-sub-menu-background: #FFFFFF;      // 子菜单背景
// $base-sub-menu-hover: #F0EBFF;           // 子菜单悬停颜色：高贵淡紫

// 商务风格3
$base-menu-color: #5A5A5A;               // 菜单文字颜色：深灰，内敛专业
$base-menu-color-active: #1890ff;        // 菜单选中颜色：科技蓝点缀，提升现代感
$base-menu-background: #F3F4F6;          // 菜单背景颜色：冷灰白，质感柔和
$base-logo-background: #2B2B2B;          // logo背景颜色：深高级灰，提升层次
$base-navbar-background: #1F1F1F;        // 顶部导航栏背景：深灰近黑，沉稳大气
$base-menu-background-active: #E6F4FF;   // 菜单选中背景颜色：浅蓝灰，理性有秩序
$base-logo-title-color: #EAEAEA;         // logo标题颜色：浅灰白，低对比不突兀
$base-menu-light-color: #6A6A6A;         // 浅色菜单文字颜色
$base-menu-light-background: #FFFFFF;    // 浅色菜单背景
$base-logo-light-title-color: #3C3C3C;   // 浅色logo标题颜色
$base-sub-menu-background: #F8F8F8;      // 子菜单背景颜色
$base-sub-menu-hover: #D9EAFD;           // 子菜单悬停颜色：淡灰蓝，保持优雅互动感






$--color-primary: #409EFF;
$--color-success: #67C23A;
$--color-warning: #E6A23C;
$--color-danger: #F56C6C;
$--color-info: #909399;

$base-sidebar-width: 150px;

// the :export directive is the magic sauce for webpack
// https://www.bluematador.com/blog/how-to-share-variables-between-js-and-sass
:export {
  menuColor: $base-menu-color;
  menuLightColor: $base-menu-light-color;
  menuColorActive: $base-menu-color-active;
  menuBackground: $base-menu-background;
  menuLightBackground: $base-menu-light-background;
  subMenuBackground: $base-sub-menu-background;
  subMenuHover: $base-sub-menu-hover;
  sideBarWidth: $base-sidebar-width;
  logoTitleColor: $base-logo-title-color;
  logoLightTitleColor: $base-logo-light-title-color;
  primaryColor: $--color-primary;
  successColor: $--color-success;
  dangerColor: $--color-danger;
  infoColor: $--color-info;
  warningColor: $--color-warning;
}
