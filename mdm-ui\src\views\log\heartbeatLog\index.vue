<template>
    <div class="lf-content">

        <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="80px">
            <el-row>
                <el-col :span="6">
                    <el-form-item label="组账号" prop="username">
                        <el-input v-model="queryParams.username" placeholder="请输入组账号" clearable
                            @keyup.enter="handleQuery" />
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="密码" prop="password">
                        <el-input v-model="queryParams.password" placeholder="请输入密码" clearable
                            @keyup.enter="handleQuery" />
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="设备MAC" prop="mac">
                        <el-input v-model="queryParams.mac" placeholder="请输入设备mac" clearable
                            @keyup.enter="handleQuery" />
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="设备IP" prop="ip">
                        <el-input v-model="queryParams.ip" placeholder="请输入设备ip" clearable @keyup.enter="handleQuery" />
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row>
                <el-col :span="6">
                    <el-form-item label="电池电量" prop="battery">
                        <el-input v-model="queryParams.battery" placeholder="请输入电池电量" clearable
                            @keyup.enter="handleQuery" />
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="wifi名称" prop="wifiName">
                        <el-input v-model="queryParams.wifiName" placeholder="请输入wifi名称" clearable
                            @keyup.enter="handleQuery" />
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="信号强度" prop="rssi">
                        <el-input v-model="queryParams.rssi" placeholder="请输入信号强度" clearable
                            @keyup.enter="handleQuery" />
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="AP-MAC" prop="apMac">
                        <el-input v-model="queryParams.apMac" placeholder="请输入apMac" clearable
                            @keyup.enter="handleQuery" />
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row>
                <el-col :span="6">
                    <el-form-item label="屏幕状态" prop="screenState" style="width: 272px;">
                        <el-select v-model="queryParams.screenState" placeholder="请选择屏幕点亮状态" clearable
                            @change="handleQuery">
                            <el-option label="息屏" value="0" />
                            <el-option label="亮屏" value="1" />
                            <el-option label="未知" value="未知" />
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="是否充电" prop="isRecharge" style="width: 272px;">
                        <el-select v-model="queryParams.isRecharge" placeholder="请选择是否充电" clearable
                            @change="handleQuery">
                            <el-option label="不充电" value="0" />
                            <el-option label="充电" value="1" />
                            <el-option label="未知" value="未知" />
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label=" ">
                        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>

        <el-row :gutter="10" class="mb8">
            <!-- <el-col :span="1.5">
                <el-button type="primary" plain icon="Plus" @click="handleAdd"
                    v-hasPermi="['log:log:add']">新增</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate"
                    v-hasPermi="['log:log:edit']">修改</el-button>
            </el-col> -->
            <el-col :span="1.5">
                <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete"
                    v-hasPermi="['log:log:remove']">删除</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="warning" plain icon="Download" @click="handleExport"
                    v-hasPermi="['log:log:export']">导出</el-button>
            </el-col>
            <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>

        <el-table v-loading="loading" :data="logList" @selection-change="handleSelectionChange" :size="size" border
            class="lf-table-content-height">
            <!-- <el-table-column type="selection" width="55" align="center" /> -->
            <el-table-column type="index" label="序号" width="50" align="center" show-overflow-tooltip />

            <el-table-column label="组账号" align="center" prop="username" width="100" show-overflow-tooltip sortable />
            <el-table-column label="密码" align="center" prop="password" width="100" show-overflow-tooltip sortable />
            <el-table-column label="设备MAC" align="center" prop="mac" width="130" show-overflow-tooltip sortable />
            <el-table-column label="设备IP" align="center" prop="ip" width="120" show-overflow-tooltip sortable />
            <el-table-column label="电池电量" align="center" prop="battery" width="110" show-overflow-tooltip sortable />
            <el-table-column label="wifi名称" align="center" prop="wifiName" width="120" show-overflow-tooltip sortable />
            <el-table-column label="信号强度" align="center" prop="rssi" width="110" show-overflow-tooltip sortable />
            <el-table-column label="apMac" align="center" prop="apMac" width="130" show-overflow-tooltip sortable />
            <el-table-column label="屏幕点亮状态" align="center" prop="screenState" width="100" show-overflow-tooltip>
                <template #default="scope">
                    <span>{{ scope.row.screenState === 1 ? '亮屏' : (scope.row.screenState === 0 ? '息屏' : '未知') }}</span>
                </template>
            </el-table-column>
            <el-table-column label="是否充电" align="center" prop="isRecharge" width="80" show-overflow-tooltip>
                <template #default="scope">
                    <span>{{ scope.row.isRecharge === 1 ? '充电' : (scope.row.isRecharge === 0 ? '不充电' : '未知') }}</span>
                </template>
            </el-table-column>
            <el-table-column label="时间" align="center" prop="createTime" width="150" show-overflow-tooltip fixed="right"
                sortable />
            <!-- <el-table-column label="所有安装程序包名" align="center" prop="allPackageName" width="200" show-overflow-tooltip /> -->
            <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="120" fixed="right"
                show-overflow-tooltip>
                <template #default="scope">
                    <!-- <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)"
                        v-hasPermi="['log:log:edit']">修改</el-button> -->
                    <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)"
                        v-hasPermi="['log:log:remove']">删除</el-button>
                </template>
            </el-table-column>
        </el-table>

        <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
            v-model:limit="queryParams.pageSize" @pagination="getList" />

        <!-- 添加或修改心跳日志对话框 -->
        <el-dialog :title="title" v-model="open" width="500px" append-to-body>
            <el-form ref="logRef" :model="form" :rules="rules" label-width="80px">
                <el-form-item label="组账号" prop="username">
                    <el-input v-model="form.username" placeholder="请输入组账号" />
                </el-form-item>
                <el-form-item label="密码" prop="password">
                    <el-input v-model="form.password" placeholder="请输入密码" />
                </el-form-item>
                <el-form-item label="mac" prop="mac">
                    <el-input v-model="form.mac" placeholder="请输入mac" />
                </el-form-item>
                <el-form-item label="ip" prop="ip">
                    <el-input v-model="form.ip" placeholder="请输入ip" />
                </el-form-item>
                <el-form-item label="电池电量" prop="battery">
                    <el-input v-model="form.battery" placeholder="请输入电池电量" />
                </el-form-item>
                <el-form-item label="wifi名称" prop="wifiName">
                    <el-input v-model="form.wifiName" placeholder="请输入wifi名称" />
                </el-form-item>
                <el-form-item label="信号强度" prop="rssi">
                    <el-input v-model="form.rssi" placeholder="请输入信号强度" />
                </el-form-item>
                <el-form-item label="apMac" prop="apMac">
                    <el-input v-model="form.apMac" placeholder="请输入apMac" />
                </el-form-item>
                <el-form-item label="屏幕点亮状态" prop="screenState">
                    <el-input v-model="form.screenState" placeholder="请输入屏幕点亮状态" />
                </el-form-item>
                <el-form-item label="是否充电" prop="isRecharge">
                    <el-input v-model="form.isRecharge" placeholder="请输入是否充电" />
                </el-form-item>
                <el-form-item label="所有安装程序包名" prop="allPackageName">
                    <el-input v-model="form.allPackageName" type="textarea" placeholder="请输入内容" />
                </el-form-item>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" @click="submitForm">确 定</el-button>
                    <el-button @click="cancel">取 消</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script setup name="Log">
import { listLog, getLog, delLog, addLog, updateLog } from "@/api/log/heartbest.js";

const { proxy } = getCurrentInstance();

const logList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

const data = reactive({
    form: {},
    queryParams: {
        pageNum: 1,
        pageSize: 20,
        username: null,
        password: null,
        mac: null,
        ip: null,
        battery: null,
        wifiName: null,
        rssi: null,
        apMac: null,
        screenState: null,
        isRecharge: null,
        allPackageName: null,
    },
    rules: {
    }
});

const { queryParams, form, rules } = toRefs(data);
const size = ref("small");

/** 查询心跳日志列表 */
function getList() {
    loading.value = true;
    listLog(queryParams.value).then(response => {
        logList.value = response.rows;
        total.value = response.total;
        loading.value = false;
    });
}

// 取消按钮
function cancel() {
    open.value = false;
    reset();
}

// 表单重置
function reset() {
    form.value = {
        id: null,
        username: null,
        password: null,
        mac: null,
        ip: null,
        battery: null,
        wifiName: null,
        rssi: null,
        apMac: null,
        screenState: null,
        isRecharge: null,
        allPackageName: null,
        createTime: null
    };
    proxy.resetForm("logRef");
}

/** 搜索按钮操作 */
function handleQuery() {
    queryParams.value.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
    ids.value = selection.map(item => item.id);
    single.value = selection.length != 1;
    multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
    reset();
    open.value = true;
    title.value = "添加心跳日志";
}

/** 修改按钮操作 */
function handleUpdate(row) {
    reset();
    const _id = row.id || ids.value
    getLog(_id).then(response => {
        form.value = response.data;
        open.value = true;
        title.value = "修改心跳日志";
    });
}

/** 提交按钮 */
function submitForm() {
    proxy.$refs["logRef"].validate(valid => {
        if (valid) {
            if (form.value.id != null) {
                updateLog(form.value).then(response => {
                    proxy.$modal.msgSuccess("修改成功");
                    open.value = false;
                    getList();
                });
            } else {
                addLog(form.value).then(response => {
                    proxy.$modal.msgSuccess("新增成功");
                    open.value = false;
                    getList();
                });
            }
        }
    });
}

/** 删除按钮操作 */
function handleDelete(row) {
    const _ids = row.id || ids.value;
    proxy.$modal.confirm('是否确认删除心跳日志编号为"' + _ids + '"的数据项？').then(function () {
        return delLog(_ids);
    }).then(() => {
        getList();
        proxy.$modal.msgSuccess("删除成功");
    }).catch(() => { });
}

/** 导出按钮操作 */
function handleExport() {
    proxy.download('log/log/export', {
        ...queryParams.value
    }, `log_${new Date().getTime()}.xlsx`)
}

getList();
</script>
<style lang="scss" scoped>
.lf-table-content-height {
    height: calc(100vh - 330px) !important;
}

:deep(.el-table__inner-wrapper:before) {
    height: 0.8px !important;
}
:deep(.el-form-item) {
    margin-bottom: 10px !important;
}
</style>
