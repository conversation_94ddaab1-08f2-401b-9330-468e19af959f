<template>
    <div class="lf-content group">
        <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px" @submit.prevent>
            <el-form-item label="组名" prop="groupName">
                <el-input v-model="queryParams.groupName" placeholder="请输入组名" clearable @keyup.enter.prevent="handleQuery" />
            </el-form-item>
            <!-- <el-form-item label="父级id" prop="parentId">
                <el-input v-model="queryParams.parentId" placeholder="请输入父级id" clearable @keyup.enter="handleQuery" />
            </el-form-item> -->
            <!-- <el-form-item label="所有祖籍" prop="parentIds">
                <el-input v-model="queryParams.parentIds" placeholder="请输入所有祖籍" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="账号" prop="userName">
                <el-input v-model="queryParams.userName" placeholder="请输入账号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="密码" prop="password">
                <el-input v-model="queryParams.password" placeholder="请输入密码" clearable @keyup.enter="handleQuery" />
            </el-form-item> -->
            <el-form-item>
                <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
                <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain icon="Plus" @click="handleAdd"
                    v-hasPermi="['pc:group:add']">新增</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="info" icon="Sort" text @click="toggleExpandAll">
                    展开/折叠
                </el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="primary" plain icon="Connection" @click="longRange"
                    v-hasPermi="['pc:group:add']">远程</el-button>
            </el-col>
            <!-- <el-col :span="1.5">
                <el-button type="info" plain icon="Close" @click="closeLongRange"
                    v-hasPermi="['pc:group:add']">关闭远程</el-button>
            </el-col> -->
            <!-- <el-col :span="1.5">
                <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate"
                    v-hasPermi="['pc:group:edit']">修改</el-button>
            </el-col> -->
            <!-- <el-col :span="1.5">
                <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete"
                    v-hasPermi="['pc:group:remove']">删除</el-button>
            </el-col> -->
            <!-- <el-col :span="1.5">
                <el-button type="warning" plain icon="Download" @click="handleExport"
                    v-hasPermi="['pc:group:export']">导出</el-button>
            </el-col> -->
            <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>


        <el-table v-if="refreshTable" v-loading="loading" :data="groupList" row-key="id"
            :default-expand-all="isExpandAll" :expand-row-keys="expandRowKeys"
            :tree-props="{ children: 'children', hasChildren: 'hasChildren' }" @selection-change="handleSelectionChange"
            :indent="12" class="lf-table-content-height" border>
            <!-- <el-table-column type="selection" width="55" align="center" /> -->
            <!-- <el-table-column type="index" width="55" align="center" label="序号" /> -->
            <!-- <el-table-column label="主键" align="center" prop="id" /> -->
            <el-table-column label="组名" prop="groupName" />
            <!-- <el-table-column label="父级id" align="center" prop="parentId" /> -->
            <!-- <el-table-column label="所有祖籍" align="center" prop="parentIds" /> -->
            <el-table-column label="账号" align="center" prop="userName" />
            <el-table-column label="密码" align="center" prop="password" />
            <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                <template #default="scope">
                    <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)"
                        v-hasPermi="['pc:group:edit']" v-if="scope.row.id != 1">修改</el-button>
                    <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)"
                        v-hasPermi="['pc:group:remove']" v-if="scope.row.id != 1">删除</el-button>
                </template>
            </el-table-column>
        </el-table>

        <!-- 添加或修改分组对话框 -->
        <el-dialog :title="title" v-model="open" width="500px" append-to-body>
            <el-form ref="groupRef" :model="form" :rules="rules" label-width="80px">
                <el-form-item label="组名" prop="groupName">
                    <el-input v-model="form.groupName" placeholder="请输入组名" />
                </el-form-item>
                <!-- <el-form-item label="父级id" prop="parentId">
                    <el-input v-model="form.parentId" placeholder="请输入父级id" />
                </el-form-item> -->
                <el-form-item label="上级菜单" prop="parentId">
                    <el-tree-select :size="size" v-model="form.parentId" :data="groupOptions"
                        :props="{ value: 'id', label: 'groupName', children: 'children' }" value-key="id"
                        placeholder="选择上级菜单" check-strictly />
                </el-form-item>
                <!-- <el-form-item label="所有祖籍" prop="parentIds">
            <el-input v-model="form.parentIds" placeholder="请输入所有祖籍" />
          </el-form-item> -->
                <el-form-item label="账号" prop="userName">
                    <el-input v-model="form.userName" placeholder="请输入账号" />
                </el-form-item>
                <el-form-item label="密码" prop="password">
                    <el-input v-model="form.password" placeholder="请输入密码" />
                </el-form-item>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" @click="submitForm">确 定</el-button>
                    <el-button @click="cancel">取 消</el-button>
                </div>
            </template>
        </el-dialog>

        <!-- 远程对话框 -->
        <el-drawer v-model="remoteOpen" @close="closeLongRange" direction="rtl" size="100%">
            <div class="remote-content">
                <!-- <img id="imageElement" alt="JPEG Image"> -->
                <canvas ref="canvas" id="imageCanvas" @click.prevent="canvasClick">
                </canvas>
            </div>
        </el-drawer>

    </div>
</template>

<script setup name="Group">
import { listGroup, getGroup, delGroup, addGroup, updateGroup } from "@/api/group";
import { nextTick, onMounted } from "vue";

const { proxy } = getCurrentInstance();

const groupList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const groupOptions = ref([]);
const group_ = ref([]);
const isExpandAll = ref(false); // 是否展开所有
const refreshTable = ref(true); // 刷新表格
const data = reactive({
    form: {},
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        groupName: null,
        parentId: null,
        parentIds: null,
        userName: null,
        password: null
    },
    rules: {
    }
});
const { queryParams, form, rules } = toRefs(data);
const ws = ref(null); // WebSocket对象
const WebSocketUrl = ref("ws://************:9098"); // WebSocket服务器地址
const remoteOpen = ref(false); // 远程对话框
const canvas = ref();
let ctx;
const canvasHeight = ref(0); // 画布高度
const canvasWidth = ref(0); // 画布宽度
const isDragging = ref(false); // 是否正在拖动
onMounted(() => {

});

nextTick(() => {
});

// 画布点击事件
// const canvasClick = (e) => {
//     const { offsetX, offsetY } = e
//     const x = parseInt(offsetX / canvasWidth.value * 100);
//     const y = parseInt(offsetY / canvasHeight.value * 100);
//     console.log(isDragging.value);
//     console.log([x, y]);
//     ws.value.send(JSON.stringify({ type: "click", value: [x, y] }));
// }


// 远程
function longRange() {
    remoteOpen.value = true;
    if (ws.value) {
        ws.value.close(); // 关闭之前的连接
    }
    ws.value = new WebSocket(WebSocketUrl.value);
    ws.value.onopen = function () {
        console.log("连接成功");
        proxy.$modal.msgSuccess("远程连接成功");
        canvas.value.onmousedown = (e) => {
            isDragging.value = true; // 开始拖动
            console.log(e)
            const { offsetX, offsetY } = e
            const x = parseInt(offsetX / canvasWidth.value * 100);
            const y = parseInt(offsetY / canvasHeight.value * 100);
            ws.value.send(JSON.stringify({ type: "moveBegin", value: [x, y] }));
        }
        canvas.value.onmousemove = (e) => {
            if (isDragging.value) {
                console.log(e);
                const { offsetX, offsetY } = e
                const x = parseInt(offsetX / canvasWidth.value * 100);
                const y = parseInt(offsetY / canvasHeight.value * 100);
                ws.value.send(JSON.stringify({ type: "moveing", value: [x, y] }));

            }
        };
        canvas.value.onmouseup = (e) => {
            // isDragging.value = false; // 停止拖动
            console.log(e);
            const { offsetX, offsetY } = e
            const x = parseInt(offsetX / canvasWidth.value * 100);
            const y = parseInt(offsetY / canvasHeight.value * 100);
            ws.value.send(JSON.stringify({ type: "moveEnd", value: [x, y] }));
            isDragging.value = false;
        };
        canvas.value.onmouseout = (e) => {
            if (isDragging.value) {
                // console.log(e);
                // const { offsetX, offsetY } = e
                // const x = parseInt(offsetX / canvasWidth.value * 100);
                // const y = parseInt(offsetY / canvasHeight.value * 100);
                // ws.value.send(JSON.stringify({ type: "moveEnd", value: [x, y] }));
                isDragging.value = false; // 继续拖动
            }
        };
        // canvas.value.onClick = (e) => {
        //     console.log(e);
        //     const { offsetX, offsetY } = e
        //     const x = parseInt(offsetX / canvasWidth.value * 100);
        //     const y = parseInt(offsetY / canvasHeight.value * 100);
        //     ws.value.send(JSON.stringify({ type: "click", value: [x, y] }));
        // };

    };
    ws.value.onmessage = function (event) {
        // console.log(event.data);
        // const imageUrl = URL.createObjectURL(event.data);
        // const imageElement = document.getElementById('imageElement');
        // imageElement.src = imageUrl;
        const blob = event.data; // 图片 Blob 对象
        const imageUrl = URL.createObjectURL(blob); // 创建图片 URL
        const image = new Image(); // 创建 Image 对象
        image.src = imageUrl; // 设置图片源
        image.onload = function () { // 图片加载完成后执行
            // const canvas = document.getElementById('imageCanvas');
            ctx = canvas.value.getContext('2d');
            const parent = canvas.value.parentElement; // 获取父元素
            const parentHeight = parent.clientHeight; // 获取父元素高度
            // 图片原始宽高
            const imgWidth = image.width;
            const imgHeight = image.height;
            // 根据父元素高度算出等比宽度
            const ratio = imgWidth / imgHeight;
            canvasHeight.value = parentHeight;
            canvasWidth.value = canvasHeight.value * ratio;
            // 设置 canvas 尺寸
            canvas.value.height = canvasHeight.value;
            canvas.value.width = canvasWidth.value;
            ctx.drawImage(image, 0, 0, canvasWidth.value, canvasHeight.value); // 绘制图片
            URL.revokeObjectURL(imageUrl); // 释放内存
        };
    }
    ws.value.onclose = function () {
        proxy.$modal.msgSuccess("远程已退出");
    }
    ws.value.onerror = function (e) {
        console.log(e);
        proxy.$modal.msgError("远程连接失败，请检查网络或服务器是否开启！");
        console.log("连接错误");
    };
}
// 关闭远程
function closeLongRange() {
    if (canvas.value && ctx) {
        ctx.clearRect(0, 0, canvas.value.width, canvas.value.height);
    }
    if (!ws.value) return;
    remoteOpen.value = false; // 关闭远程对话框
    ws.value.close(); // 关闭 WebSocket 连接
   
};

/** 查询分组列表 */
function getList() {
    loading.value = true;
    listGroup(queryParams.value).then(response => {
        console.log(response)
        groupList.value = proxy.handleTree(response);
        loading.value = false;
    });
}
/** 查询菜单下拉树结构 */
function getTreeselect() {
    groupOptions.value = [];
    listGroup().then(response => {
        group_.value = response;
        let group;
        group = proxy.handleTree(response)[0];
        groupOptions.value.push(group);
        console.log(groupOptions.value);
    });
}
// 取消按钮
function cancel() {
    open.value = false;
    reset();
}

// 表单重置
function reset() {
    form.value = {
        id: null,
        groupName: null,
        parentId: null,
        parentIds: null,
        userName: null,
        password: null
    };
    proxy.resetForm("groupRef");
}

/** 搜索按钮操作 */
function handleQuery() {
    queryParams.value.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
    ids.value = selection.map(item => item.id);
    single.value = selection.length != 1;
    multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
    reset();
    getTreeselect();
    open.value = true;
    title.value = "添加分组";
}

/** 修改按钮操作 */
function handleUpdate(row) {
    console.log(row);
    reset();
    getTreeselect();
    const _id = row.id || ids.value
    getGroup(_id).then(response => {
        form.value = response.data;
        open.value = true;
        title.value = "修改分组";
    });
}

/** 展开/折叠操作 */
function toggleExpandAll() {
    refreshTable.value = false;
    isExpandAll.value = !isExpandAll.value;
    nextTick(() => {
        refreshTable.value = true;
    });
}
/** 提交按钮 */
function submitForm() {
    proxy.$refs["groupRef"].validate(valid => {
        if (valid) {
            if (form.value.id != null) {
                updateGroup(form.value).then(response => {
                    proxy.$modal.msgSuccess("修改成功");
                    open.value = false;
                    getList();
                });
            } else {
                addGroup(form.value).then(response => {
                    proxy.$modal.msgSuccess("新增成功");
                    open.value = false;
                    getList();
                });
            }
        }
    });
}

/** 删除按钮操作 */
function handleDelete(row) {
    const _ids = row.id || ids.value;
    proxy.$modal.confirm('是否确认删除分组编号为"' + _ids + '"的数据项？').then(function () {
        return delGroup(_ids);
    }).then(() => {
        getList();
        proxy.$modal.msgSuccess("删除成功");
    }).catch(() => { });
}

/** 导出按钮操作 */
function handleExport() {
    proxy.download('pc/group/export', {
        ...queryParams.value
    }, `group_${new Date().getTime()}.xlsx`)
}

getList();
</script>
<style lang="scss" scoped>
.group {
    .lf-table-content-height {
        height: calc(100vh - 225px) !important;
    }

    :deep(.el-table__inner-wrapper:before) {
        height: 0.8px !important;
    }

    // 输入框的margin-bottom
    :deep(.el-form-item) {
        margin-bottom: 8px !important;
    }

    :deep(.el-drawer__header) {
        margin-bottom: 5px;
    }

    :deep(.el-drawer__body) {
        padding: 0px;
    }

    .remote-content {
        width: 100%;
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        background-color: #f5f5f5;


        #imageCanvas {
            display: block;
            height: 100%;
            width: auto;
            touch-action: none;
        }

        #imageElement {
            height: 100%;
            // object-fit: cover;
        }
    }
}
</style>