<template>
  <div class="lf-content group">
    <div class="menu" v-show="leftShow">
      <el-row justify="space-around">
        <el-col :span="18">
          <el-input v-model="queryParams.groupName" placeholder="点击输入组名" clearable @keyup.enter="getList" />
        </el-col>
        <el-col :span="1.5">
          <el-button text bg icon="RefreshRight" @click="groupRefresh"></el-button>
        </el-col>
      </el-row>
      <el-divider />
      <div class="menu-tree">
        <el-tree ref="treeRef" style="max-width: 250px" :data="groupList" default-expand-all
          :expand-on-click-node="false" highlight-current :props="props" :indent="6" @node-click="handleNodeClick">
          <template #default="{ node, data }">
            <div class="menu-tree-item">
              <span>{{ node.label }}</span>
              <div class="btn-group">
                <el-button type="primary" text @click.stop.prevent="handleGroupAdd(data)" icon="Plus"
                  size="small"></el-button>
                <el-button type="primary" text @click.stop.prevent="handleGroupEdit(data)" icon="Setting"
                  size="small"></el-button>
                <el-button type="danger" text @click.stop.prevent="handleGroupDelete(data)" icon="Delete" size="small"
                  v-if="data.id != '1'"></el-button>
              </div>
            </div>
          </template>
        </el-tree>
      </div>
      <div class="divid" @click="checkDivid('left')">
        <el-icon>
          <ArrowLeft />
        </el-icon>
      </div>
    </div>

    <div class="content" v-show="rightShow">
      <div class="divid" @click="checkDivid('right')" v-if="!leftShow">
        <el-icon>
          <ArrowRight />
        </el-icon>
      </div>
      <div class="content_top">
        <el-row :gutter="10" justify="space-between" align="middle">
          <el-col :span="1.5" style="margin-left: 10px" >
            <GroupApk :groupCurrentId="groupCurrentId" :groupCurrent="groupCurrent"></GroupApk>
          </el-col>
          <el-col :span="1.5" style="margin-left: 10px" >
            <ChangeAllGroup :ids="ids"></ChangeAllGroup>
          </el-col>
          <el-col :span="6">
            <el-input placeholder="输入关键字" size="large" v-model="deviceParams.where" @keyup.enter="getDeviceList"
              clearable></el-input>
          </el-col>
          <el-col :span="1.5">
            <el-button type="primary" size="large" @click="getDeviceList">搜索</el-button>
          </el-col>
          
          <el-col :span="1.5" style="margin-left: 10px" >
            <SelectPushApk :ids="ids" :pushIds="pushIds" ></SelectPushApk>
          </el-col>
          <!-- <el-col :span="1.5" style="margin-left: auto">
            <GridControl :ids="pushIds"></GridControl>
          </el-col> -->

          <el-col :span="1.5" style="margin-left: auto">
            <showCloumn :columns="columns"></showCloumn>
          </el-col>
        </el-row>
      </div>

      <div class="content_bottom">
        <el-table v-if="refreshTable" v-loading="loading" :data="deviceList" size="small"
          @selection-change="handleSelectionChange" :default-expand-all="isExpandAll" border :indent="6"
          class="lf-table-content-height" @row-dblclick="handleUpdate" :row-class-name="tableRowClassName">
          <el-table-column type="selection" width="50" align="center" sortable />
          <el-table-column label="组名" prop="groupName" align="center" width="100" show-overflow-tooltip
            v-if="columns[0].visible" fixed="left" sortable >
            <template #default="{ row }" >
              <!-- <span style="font-style: italic; font-weight: bold">{{
                row.groupName
              }}</span> -->
              <ChangeGroup :row="row" />
            </template>
          </el-table-column>
          <el-table-column label="动作" prop="apkIds" align="center" width="100" show-overflow-tooltip
            v-if="columns[1].visible" fixed="left" sortable >
            <template #default="scope">
              <GridControl :ids="[{ ...scope.row }]" size="small" :bg="false"></GridControl>
            </template>
          </el-table-column>
          <el-table-column label="设备型号" prop="model" align="center" width="100" show-overflow-tooltip
            v-if="columns[2].visible" sortable>
            <template #default="scope">
                <EquipmentDetails :row="scope.row" />
            </template>
          </el-table-column>
          <el-table-column label="在线状态" prop="status" align="center" width="100" show-overflow-tooltip
            v-if="columns[3].visible" sortable >
            <template #default="{ row }">
              <el-tag :type="getStatusType(row.status)" effect="light" size="small">
                {{ getStatusLabel(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="mac地址" prop="mac" align="center" width="150" show-overflow-tooltip
            v-if="columns[4].visible" sortable />
          <el-table-column label="ip地址" prop="ip" align="center" width="100" show-overflow-tooltip
            v-if="columns[5].visible" sortable />
          <el-table-column label="电池电量" prop="battery" align="center" width="120" show-overflow-tooltip
            v-if="columns[6].visible" sortable >
            <template #default="{ row }">
              <span class="battery">
                {{}}
                <div>
                  <Battery :value="row.battery"></Battery>
                </div>
              </span>
            </template>
          </el-table-column>
          <el-table-column label="是否充电" prop="isRecharge" align="center" width="100" show-overflow-tooltip
            v-if="columns[20].visible" sortable />
          <el-table-column label="Essid" prop="wifiName" align="center" width="150" show-overflow-tooltip
            v-if="columns[7].visible" sortable />
          <el-table-column label="RSSI" prop="rssi" align="center" width="120" show-overflow-tooltip
            v-if="columns[8].visible" sortable >
            <template #default="{ row }">
              <span class="wifi-signal">
                <div>
                  <Wifi :rssi="row.rssi"></Wifi>
                </div>
              </span>
            </template>
          </el-table-column>
          <el-table-column label="AP-MAC" prop="apMac" align="center" width="150" show-overflow-tooltip
            v-if="columns[19].visible" sortable />
          <el-table-column label="设备sn" prop="sn" align="center" width="120" show-overflow-tooltip
            v-if="columns[18].visible" sortable />
          <el-table-column label="资产编号" prop="assetCode" align="center" width="150" show-overflow-tooltip
            v-if="columns[9].visible" sortable />
          <el-table-column label="设备名称" prop="deviceName" align="center" width="150" show-overflow-tooltip
            v-if="columns[10].visible" sortable />
          <el-table-column label="平台" prop="platform" align="center" width="120" show-overflow-tooltip
            v-if="columns[11].visible" sortable />
          <el-table-column label="版本" prop="version" align="center" width="80" show-overflow-tooltip
            v-if="columns[12].visible" sortable />
          <el-table-column label="位置" prop="location" align="center" width="150" show-overflow-tooltip
            v-if="columns[13].visible" sortable />
          <el-table-column label="当天活跃时长" prop="activityTime" align="center" width="150" show-overflow-tooltip
            v-if="columns[14].visible" sortable >
            <template #default="{ row }">
              <span>{{ row.activityTime }} 分钟</span>
            </template>
          </el-table-column>
          <el-table-column label="剩余存储" prop="remainingStorage" align="center" width="150" show-overflow-tooltip
            v-if="columns[15].visible" sortable />
          <el-table-column label="上次心跳时间" prop="heartbeatTime" align="center" width="150" show-overflow-tooltip
            v-if="columns[16].visible" fixed="right" sortable />
          <el-table-column label="备注" prop="remark" align="center" width="150" show-overflow-tooltip
            v-if="columns[17].visible" sortable />
          <el-table-column label="操作" align="center" fixed="right" width="180">
            <template #default="{ row }">
              <MobileApk :row="row" />
              <SendMessage :row="row" />
              <!-- <el-link type="primary" :underline="false" @click="handleApk(row.apkIds)">操作</el-link> -->

            </template>
          </el-table-column>
          <!-- <el-table-column label="设备id" prop="deviceId" align="center" width="150" show-overflow-tooltip
            v-if="columns[18].visible" /> -->

        </el-table>
        <pagination v-show="total > 0" :total="total" v-model:page="deviceParams.pageNum"
          v-model:limit="deviceParams.pageSize" @pagination="getDeviceList" />
      </div>
    </div>

    <!-- 添加或修改分组对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="groupRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="组名" prop="groupName">
          <el-input v-model="form.groupName" placeholder="请输入组名" />
        </el-form-item>
        <el-form-item label="账号" prop="userName">
          <el-input v-model="form.userName" placeholder="请输入账号" />
        </el-form-item>
        <el-form-item label="密码" prop="password">
          <el-input v-model="form.password" placeholder="请输入密码" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 添加或修改设备对话框 -->
    <el-dialog :title="titleDevice" v-model="openDevice" width="500px" append-to-body>
      <el-form ref="deviceRef" :model="formDevice" label-width="80px">
        <el-form-item label="SN" prop="sn">
          <el-input v-model="formDevice.sn" placeholder="请输入设备SN" disabled />
        </el-form-item>
        <el-form-item label="资产编号" prop="assetCode">
          <el-input v-model="formDevice.assetCode" placeholder="请输入资产编号" />
        </el-form-item>
        <el-form-item label="设备名称" prop="deviceName">
          <el-input v-model="formDevice.deviceName" placeholder="请输入设备名称" />
        </el-form-item>
        <el-form-item label="备注" prop="password">
          <el-input v-model="formDevice.remark" type="textarea" :rows="3" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitDeviceForm">确 定</el-button>
          <el-button @click="cancelDevice">取 消</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 远程对话框 -->
    <el-drawer v-model="remoteOpen" @close="closeLongRange" direction="rtl" size="100%">
      <div class="remote-content">
        <div class="power">
          <el-button text bg type="danger" icon="SwitchButton">电源</el-button>
        </div>
        <!-- <img id="imageElement" alt="JPEG Image"> -->
        <canvas ref="canvas" id="imageCanvas" @click.prevent="canvasClick">
        </canvas>
      </div>
    </el-drawer>
  </div>
</template>

<script setup name="Group">
import { listGroup, getGroup, delGroup, addGroup, updateGroup, listDevice, getDevice, updateDevice, addDevice } from "@/api/group";
import showCloumn from "@/components/showColumn/index.vue";
import Battery from "@/components/Battery/index.vue";
import Wifi from "@/components/Wifi/index.vue";
import GroupApk from "@/components/GroupApk/index.vue";
import SelectPushApk from "@/components/SelectPushApk/index.vue";
import MobileApk from "@/components/mobileApk/index.vue";
import GridControl from "@/components/GridControl/index.vue";
import ChangeGroup from "@/components/ChangeGroup/index.vue";
import ChangeAllGroup from "@/components/ChangeAllGroup/index.vue";
import EquipmentDetails from "@/components/EquipmentDetails/index.vue";
import SendMessage from "@/components/SendMessage/index.vue";
import { ElMessageBox, ElMessage } from "element-plus";
import { nextTick, onMounted } from "vue";

const { proxy } = getCurrentInstance();
const props = ref({
  label: "groupName",
});
const groupList = ref([]);
const groupCurrent = ref(null); // 当前选中分组
const groupCurrentId = ref(null); // 当前选中分组id
const open = ref(false);
const openDevice = ref(false); // 打开设备对话框
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const pushIds = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const titleDevice = ref("");
const groupOptions = ref([]);
const group_ = ref([]);
const isExpandAll = ref(false); // 是否展开所有
const refreshTable = ref(true); // 刷新表格
const data = reactive({
  form: {},
  formDevice: {},
  queryParams: {
    groupName: null,
    parentId: null,
    parentIds: null,
    userName: null,
    password: null,
  },
  rules: {
    groupName: [
      { required: true, message: "组名不能为空", trigger: "blur" },
      {
        min: 2,
        max: 20,
        message: "组名长度在3到20个字符之间",
        trigger: "blur",
      },
    ],
    userName: [
      { required: true, message: "账号不能为空", trigger: "blur" },
      {
        pattern: /^[a-zA-Z0-9_]+$/,
        message: "账号只能包含字母、数字和下划线",
        trigger: "blur",
      },
      {
        min: 3,
        max: 20,
        message: "账号长度在3到20个字符之间",
        trigger: "blur",
      },
    ],
    password: [
      { required: true, message: "密码不能为空", trigger: "blur" },
      {
        pattern: /^[a-zA-Z0-9_]+$/,
        message: "密码只能包含字母、数字和下划线",
        trigger: "blur",
      },
      {
        min: 6,
        max: 20,
        message: "密码长度在6到20个字符之间",
        trigger: "blur",
      },
    ],

  },
});
const { queryParams, form, formDevice, rules } = toRefs(data);
const deviceList = ref([]); // 设备列表
const deviceParams = reactive({
  pageNum: 1,
  pageSize: 20,
  where: null,
  groupId: null,
});
const ws = ref(null); // WebSocket对象
const WebSocketUrl = ref(null); // WebSocket服务器地址
const remoteOpen = ref(false); // 远程对话框
const canvas = ref();
let ctx;
const canvasHeight = ref(0); // 画布高度
const canvasWidth = ref(0); // 画布宽度
const isDragging = ref(false); // 是否正在拖动
const columns = ref([
  { key: 0, label: "组名", visible: true },
  { key: 1, label: "动作", visible: true },
  { key: 2, label: "设备型号", visible: true },
  { key: 3, label: "在线状态", visible: true },
  { key: 4, label: "mac地址", visible: true },
  { key: 5, label: "ip地址", visible: true },
  { key: 6, label: "电池电量", visible: true },
  { key: 7, label: "Essid", visible: true },
  { key: 8, label: "RSSI", visible: true },
  { key: 9, label: "资产编号", visible: true },
  { key: 10, label: "设备名称", visible: true },
  { key: 11, label: "平台", visible: false },
  { key: 12, label: "版本", visible: false },
  { key: 13, label: "位置", visible: false },
  { key: 14, label: "当天活跃时长", visible: true },
  { key: 15, label: "剩余存储", visible: false },
  { key: 16, label: "上次心跳时间", visible: true },
  { key: 17, label: "备注", visible: true },
  // { key: 18, label: "设备id", visible: false },
  { key: 18, label: "设备sn", visible: true },
  { key: 29, label: "AP-MAC", visible: true },
  { key: 20, label: "是否充电", visible: true },
]);
onMounted(() => { });

nextTick(() => { });

// 远程
// function longRange(ip) {
//     remoteOpen.value = true;
//     if (ws.value) {
//         ws.value.close(); // 关闭之前的连接
//     }
//     WebSocketUrl.value = `ws://${ip}:9098`;
//     ws.value = new WebSocket(WebSocketUrl.value);
//     ws.value.onopen = function () {
//         console.log("连接成功");
//         proxy.$modal.msgSuccess("远程连接成功");
//         canvas.value.onmousedown = (e) => {
//             isDragging.value = true; // 开始拖动
//             console.log(e)
//             const { offsetX, offsetY } = e
//             const x = parseInt(offsetX / canvasWidth.value * 100);
//             const y = parseInt(offsetY / canvasHeight.value * 100);
//             console.log(x, y);
//             ws.value.send(JSON.stringify({ type: "moveBegin", value: [x, y] }));
//         }
//         canvas.value.onmousemove = (e) => {
//             if (isDragging.value) {
//                 console.log(e);
//                 const { offsetX, offsetY } = e
//                 const x = parseInt(offsetX / canvasWidth.value * 100);
//                 const y = parseInt(offsetY / canvasHeight.value * 100);
//                 ws.value.send(JSON.stringify({ type: "moveing", value: [x, y] }));

//             }
//         };
//         canvas.value.onmouseup = (e) => {
//             // isDragging.value = false; // 停止拖动
//             console.log(e);
//             const { offsetX, offsetY } = e
//             const x = parseInt(offsetX / canvasWidth.value * 100);
//             const y = parseInt(offsetY / canvasHeight.value * 100);
//             ws.value.send(JSON.stringify({ type: "moveEnd", value: [x, y] }));
//             isDragging.value = false;
//         };
//         canvas.value.onmouseout = (e) => {
//             if (isDragging.value) {
//                 isDragging.value = false; // 继续拖动
//             }
//         }

//     };
//     ws.value.onmessage = function (event) {
//         const blob = event.data; // 图片 Blob 对象
//         const imageUrl = URL.createObjectURL(blob); // 创建图片 URL
//         const image = new Image(); // 创建 Image 对象
//         image.src = imageUrl; // 设置图片源
//         image.onload = function () { // 图片加载完成后执行
//             // const canvas = document.getElementById('imageCanvas');
//             ctx = canvas.value.getContext('2d');
//             const parent = canvas.value.parentElement; // 获取父元素
//             const parentHeight = parent.clientHeight; // 获取父元素高度
//             // 图片原始宽高
//             const imgWidth = image.width;
//             const imgHeight = image.height;
//             // 根据父元素高度算出等比宽度
//             const ratio = imgWidth / imgHeight;
//             canvasHeight.value = parentHeight;
//             canvasWidth.value = canvasHeight.value * ratio;
//             // 设置 canvas 尺寸
//             canvas.value.height = canvasHeight.value;
//             canvas.value.width = canvasWidth.value;
//             ctx.drawImage(image, 0, 0, canvasWidth.value, canvasHeight.value); // 绘制图片
//             URL.revokeObjectURL(imageUrl); // 释放内存
//         };
//     }
//     ws.value.onclose = function () {
//         proxy.$modal.msgSuccess("远程已退出");
//     }
//     ws.value.onerror = function (e) {
//         console.log(e);
//         proxy.$modal.msgError("远程连接失败，请检查网络或服务器是否开启！");
//         console.log("连接错误");
//     };
// }
// 关闭远程
function closeLongRange() {
  if (canvas.value && ctx) {
    ctx.clearRect(0, 0, canvas.value.width, canvas.value.height);
  }
  if (!ws.value) return;
  remoteOpen.value = false; // 关闭远程对话框
  ws.value.close(); // 关闭 WebSocket 连接
}

/** 查询分组列表 */
function getList() {
  listGroup(queryParams.value).then((response) => {
    groupList.value = proxy.handleTree(response);
  });
}
/** 查询设备列表 */
function getDeviceList() {
  loading.value = true;
  listDevice(deviceParams).then((response) => {
    deviceList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

// 分组刷新
function groupRefresh() {
  queryParams.value.groupName = null;
  groupCurrent.value = null;
  deviceParams.groupId = null;
  getList();
  getDeviceList();
}
// 组点击事件
function handleNodeClick(data) {
  console.log(data);

  groupCurrent.value = data.groupName;
  groupCurrentId.value = data.id;
  deviceParams.groupId = data.id;
  getDeviceList();
}
// 组新建
function handleGroupAdd(data) {
  console.log(data);
  reset();
  open.value = true;
  title.value = "添加分组";
  form.value.parentId = data.id;
}
// 组修改
function handleGroupEdit(data) {
  reset();
  getGroup(data.id).then((res) => {
    console.log(res);
    open.value = true;
    title.value = "修改分组";
    form.value = res.data;
  });
  // form.value.parentId = row.parentId;
}
// 组删除
function handleGroupDelete(data) {
  console.log(data);
  ElMessageBox.confirm(
    `确定要删除组名为-- ${data.groupName} --的组 吗？`,
    "提示",
    {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    }
  )
    .then(() => {
      delGroup(data.id).then(
        (res) => {
          // 这里执行删除操作
          ElMessage.success("删除成功");
          getList();
          getDeviceList();
        },
        (e) => {
          ElMessage.error(e.message);
        }
      );
    })
    .catch(() => {
      ElMessage.info("取消删除");
    });
}

// 处理电池电量
const getBatteryColor = (value) => {
  if (value === null || value === undefined || value === "") {
    return "gray";
  } else if (value > 75) {
    return "green";
  } else if (value > 30) {
    return "orange";
  } else {
    return "red";
  }
};

// 处理设备在线状态
const getStatusType = (status) => {
  switch (status) {
    case 0:
      return "danger"; // 红色 - 离线
    case 1:
      return "success"; // 绿色 - 在线
    case 2:
      return "warning"; // 橙色 - 未激活
    default:
      return "info"; // 灰色 - 未知
  }
};
const getStatusLabel = (status) => {
  switch (status) {
    case 0:
      return "离线";
    case 1:
      return "在线";
    case 2:
      return "未激活";
    default:
      return "未知";
  }
};

/** 查询菜单下拉树结构 */
function getTreeselect() {
  groupOptions.value = [];
  listGroup().then((response) => {
    group_.value = response;
    let group;
    group = proxy.handleTree(response)[0];
    groupOptions.value.push(group);
    console.log(groupOptions.value);
  });
}
// 取消按钮
function cancel() {
  open.value = false;
  reset();
}
// 取消设备对话框
function cancelDevice() {
  openDevice.value = false;
  formDevice.value = {
    id: null,
    assetCode: null,
    deviceName: null,
    remark: null,
  };
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    groupName: null,
    parentId: null,
    parentIds: null,
    userName: null,
    password: null,
  };
  proxy.resetForm("groupRef");
}
/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}
/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}
// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.id);
  pushIds.value = selection.map((item) => item);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}
/** 新增按钮操作 */
function handleAdd() {
  reset();
  getTreeselect();
  open.value = true;
  title.value = "添加分组";
}
/** 修改按钮操作 */
function handleUpdate(row) {
  formDevice.value = {
    id: null,
    assetCode: null,
    deviceName: null,
    remark: null,
  };
  getDevice(row.id).then((response) => {
    console.log(response);
    formDevice.value = response.data;
    openDevice.value = true;
    titleDevice.value = "修改设备信息";
  });
}
/** 展开/折叠操作 */
function toggleExpandAll() {
  refreshTable.value = false;
  isExpandAll.value = !isExpandAll.value;
  nextTick(() => {
    refreshTable.value = true;
  });
}
/** 提交按钮 */
function submitForm() {
  proxy.$refs["groupRef"].validate((valid) => {
    if (valid) {
      if (form.value.id != null) {
        updateGroup(form.value).then((response) => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addGroup(form.value).then((response) => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}
/** 提交按钮 */
function submitDeviceForm() {
  proxy.$refs["deviceRef"].validate((valid) => {
    if (valid) {
      if (formDevice.value.id != null) {
        updateDevice(formDevice.value).then((response) => {
          proxy.$modal.msgSuccess("修改成功");
          openDevice.value = false;
          getDeviceList();
        });
      } else {
        addDevice(formDevice.value).then((response) => {
          proxy.$modal.msgSuccess("新增成功");
          openDevice.value = false;
          getDeviceList();
        });
      }
    }
  });
}
/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value;
  proxy.$modal
    .confirm('是否确认删除分组编号为"' + _ids + '"的数据项？')
    .then(function () {
      return delGroup(_ids);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    })
    .catch(() => { });
}
/** 导出按钮操作 */
function handleExport() {
  proxy.download(
    "pc/group/export",
    {
      ...queryParams.value,
    },
    `group_${new Date().getTime()}.xlsx`
  );
}
getList();
getDeviceList();
// checkDivid
const leftShow = ref(true);
const rightShow = ref(true);
function checkDivid(type) {
  if (type === "left") {
    leftShow.value = false;
  } else if (type === "right") {
    leftShow.value = true;
  }
}



//  指令
const socket = ref(null); // WebSocket对象
// 根据在线状态设置颜色status
function tableRowClassName({ row }) {
  if(row.status == 0) {
    return 'error-row';
  }else if(row.status == 1) {
    return 'success-row';
  }else if(row.status == 2) {
    return 'warning-row';
  }
}
// function setSound(ip,flag) {
//     proxy.$modal.msgSuccess(""+flag ? 'open': 'close'+"声音成功");
//     const timeout = 1000; // 设定连接超时时间（毫秒）
//     let didConnect = false;
//     socket.value = new WebSocket(`ws://${ip}:8080`);
//     // 设置超时处理逻辑
//     const timer = setTimeout(() => {
//         if (!didConnect) {
//             socket.value.close();
//             ElNotification({
//                 title: '连接超时',
//                 message: '设备连接超时，请检查网络或设备状态',
//                 type: 'error',
//                 position: 'top-left',
//             });
//         }
//     }, timeout);
//     socket.value.onopen = function () {
//         didConnect = true;
//         clearTimeout(timer); // 连接成功，清除超时定时器
//         console.log("WebSocket 已连接");
//         // 发送获取 APK 列表的请求
//         // getApkDataList();
//         if(flag) {
//             socket.value.send(JSON.stringify([{
//                 type: "set",
//                 value: "openSound"
//             }]))
//         }else {
//             socket.value.send(JSON.stringify([{
//                 type: "set",
//                 value: "closeSound"
//             }]))
//         }
//     };
//     socket.value.onmessage = function (event) {

//     };
//     socket.value.onerror = function () {
//         clearTimeout(timer); // 错误也清除超时逻辑
//         ElNotification({
//             title: '错误',
//             message: "设备连接失败或不在线",
//             type: 'error',
//         });
//     };
// }
</script>
<style lang="scss" scoped>
.group {
  padding: 10px !important;
  display: flex;
  flex-direction: row;
  background-color: #f5f5f5 !important;
  position: relative;

  .menu {
    width: 250px;
    height: 100%;
    // overflow: auto;
    background-color: #fff;
    border: 1px solid #e4e7ed;
    padding: 8px;
    box-sizing: border-box;
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    position: relative;
    transition: all 0.3s ease-in-out;

    .divid {
      position: absolute;
      top: 0;
      bottom: 0;
      right: 0;
      margin: auto;
      width: 10px;
      height: 20px;
      background-color: #e4e7ed;
      border-radius: 4px 0 0 4px;
      transition: all 0.3s ease;
    }

    .el-button {
      padding: 12px !important;
    }

    .el-divider {
      margin: 10px 0 !important;
    }

    .menu-tree {
      flex: 1;
      overflow: auto;
      scrollbar-width: none;
      /* Firefox */
      -ms-overflow-style: none;

      /* IE 10+ */
      ::-webkit-scrollbar {
        display: none;
        /* Chrome, Safari, Opera */
      }

      .menu-tree-item {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0px 10px 0px 0px;

        .el-button {
          padding: 4px !important;
          margin-left: 0px !important;
        }

        .btn-group {
          display: flex;
          gap: 2px;
        }
      }
    }
  }

  .content {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    margin-left: 10px;
    position: relative;
    transition: all 0.3s ease-in-out;

    .divid {
      position: absolute;
      top: 0;
      bottom: 0;
      left: 0;
      margin: auto;
      width: 10px;
      height: 20px;
      background-color: #e4e7ed;
      border-radius: 0 4px 4px 0;
      transition: all 0.3s ease;
    }

    .content_top {
      height: 80px;
      width: 100%;
      background-color: #fff;
      border-radius: 8px;
      box-sizing: border-box;
      margin-bottom: 10px;
      border: 1px solid #e4e7ed;
      padding: 11px;
      box-sizing: border-box;

      .el-button {
        padding: 12px !important;
      }
    }

    .content_bottom {
      flex: 1;
      width: 100%;
      background-color: #fff;
      border-radius: 8px;
      box-sizing: border-box;
      border: 1px solid #e4e7ed;
      padding: 10px;

      .battery {
        width: 100%;
      }
    }
  }

  .divid {
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 12px;
    color: #722ed1;
  }

  .divid:hover {
    transform: scale(1.2);
  }

  .lf-table-content-height {
    height: calc(100vh - 235px) !important;
  }

  :deep(.el-table__inner-wrapper:before) {
    height: 0.8px !important;
  }

  // 输入框的margin-bottom
  :deep(.el-form-item) {
    margin-bottom: 8px !important;
  }

  :deep(.el-drawer__header) {
    margin-bottom: 5px;
  }

  :deep(.el-drawer__body) {
    padding: 0px;
  }

  // 动作
  :deep(.cell) {
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .remote-content {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #f5f5f5;
  }

  :deep(.el-transfer-panel) {
    width: 500px !important;
  }
}
:deep(.el-table .error-row) {
  --el-table-tr-bg-color: #fef0f0;
}
:deep(.el-table .success-row) {
  --el-table-tr-bg-color: var(--el-color-success-light-9);
}
:deep(.el-table .warning-row) {
  --el-table-tr-bg-color: #fff5e6;
}
</style>
