import request from '@/utils/request'

// 查询定时推送列表
export function listJob(query) {
  return request({
    url: '/job/job/list',
    method: 'get',
    params: query
  })
}

// 查询定时推送详细
export function getJob(id) {
  return request({
    url: '/job/job/' + id,
    method: 'get'
  })
}

// 新增定时推送
export function addJob(data) {
  return request({
    url: '/job/job',
    method: 'post',
    data: data
  })
}

// 修改定时推送
export function updateJob(data) {
  return request({
    url: '/job/job',
    method: 'put',
    data: data
  })
}

// 删除定时推送
export function delJob(id) {
  return request({
    url: '/job/job/' + id,
    method: 'delete'
  })
}

// 获取设备信息
export function getDeviceByJobId(id) {
  return request({
    url: '/job/job/getDeviceByJobId/' + id,
    method: 'get'
  })
}

// 获取APK信息
export function getApkByJobId(id) {
  return request({
    url: '/job/job/getApkByJobId/' + id,
    method: 'get'
  })
}
