<template>
    <div class="lf-content group">
        <div class="menu" v-show="leftShow">
            <el-row justify="space-around">
                <el-col :span="18">
                    <el-input v-model="fileFilterText" placeholder="回车搜索" @keyup.enter="getList" />
                </el-col>
                <el-col :span="1.5">
                    <el-button text bg icon="RefreshRight" @click="fileRefresh"></el-button>
                </el-col>
            </el-row>
            <el-divider />
            <div class="menu-tree">
                <!-- @node-contextmenu="handleContextMenu" -->
                <el-tree ref="treeRef" style="max-width: 250px" :data="fileList" default-expand-all
                    :expand-on-click-node="false" highlight-current :props="props" :indent="6"
                    @node-click="handleNodeClick">
                    <template #default="{ node, data }">
                        <div class="menu-tree-item">
                            <span>{{ node.label }}</span>
                            <div class="btn-group">
                                <el-button type="primary" text @click.stop.prevent="handleContextMenu(node, data)"
                                    icon="Plus" size="small"></el-button>
                                <el-button type="danger" text @click.stop.prevent="handleFileDelete(node, data)"
                                    icon="Delete" size="small" v-if="node.label !== 'filePath'"></el-button>
                            </div>
                        </div>
                    </template>
                </el-tree>
            </div>
            <div class="divid" @click="checkDivid('left')">
                <el-icon><ArrowLeft /></el-icon>
            </div>
        </div>
        <div class="content" v-show="rightShow">
            <div class="divid" @click="checkDivid('right')" v-if="!leftShow">
                <el-icon><ArrowRight /></el-icon>
            </div>
            <div class="content_top">
                <el-row :gutter="10" justify="space-between" align="middle">
                    <el-col :span="1.5">
                        <el-button type="primary" text bg size="large" @click="handleImport">上传文件</el-button>
                    </el-col>
                    <el-col :span="4">
                        <el-input placeholder="文件名" size="large" v-model="apkParams.apkName"
                            @keyup.enter="getApkDataList"></el-input>
                    </el-col>
                    <el-col :span="1.5">
                        <el-button type="primary" size="large" @click="getApkDataList">搜索</el-button>
                    </el-col>
                    <el-col :span="1.5" style="margin-left:auto;">
                        <showCloumn :columns="columns"></showCloumn>
                    </el-col>
                </el-row>
            </div>
            <div class="content_bottom">
                <el-table v-if="refreshTable" v-loading="loading" :data="apkList" size="small" border :indent="12"
                    stripe class="lf-table-content-height">
                    <el-table-column type="index" label="序号" width="50" align="center"></el-table-column>
                    <el-table-column label="文件名" prop="apkName" width="180" show-overflow-tooltip
                        v-if="columns[0].visible" sortable>
                        <template #default="scope">
                            <div style="display: flex;justify-content: start;align-items: center;padding: 0 10px;">
                                <img
                                    :src="'http://' + scope.row.apkImage"
                                    alt=""
                                    style="width: 28px;height: 28px;"
                                    @error="e => e.target.src = defaultImg"
                                />
                                <span style="padding-left: 10px;">{{ scope.row.apkName }}</span>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column label="包名" prop="apkPackage" width="150" show-overflow-tooltip
                        v-if="columns[1].visible" sortable>
                        <template #default="scope">
                            <span v-if="scope.row.apkName && scope.row.apkName.endsWith('.apk')">{{ scope.row.apkPackage }}</span>
                            <span v-else>-/-</span>
                        </template>
                    </el-table-column>
                    <!-- <el-table-column label="文件地址" prop="filePath" width="180" show-overflow-tooltip
                        v-if="columns[2].visible" sortable >
                        <template #default="scope">
                            <div v-html="scope.row.filePath" style="text-align: left;"></div>
                        </template>
                    </el-table-column> -->
                    <el-table-column label="版本" prop="version" width="120" show-overflow-tooltip
                        v-if="columns[2].visible" sortable >
                        <template #default="scope">
                            <span v-if="scope.row.apkName && scope.row.apkName.endsWith('.apk')">{{ scope.row.version }}</span>
                            <span v-else>-/-</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="安装数量" prop="num" align="center" width="120" show-overflow-tooltip
                        v-if="columns[3].visible" sortable >
                            <template #default="scope">
                                <ApkDownloadNum :row="scope.row" v-if="scope.row.apkName && scope.row.apkName.endsWith('.apk')"></ApkDownloadNum>
                                <span v-else>-/-</span>
                            </template>
                        </el-table-column>
                    <el-table-column label="versionCode" prop="versionCode" align="center" width="120"
                        show-overflow-tooltip v-if="columns[4].visible" sortable >
                        <template #default="scope">
                            <span v-if="scope.row.apkName && scope.row.apkName.endsWith('.apk')">{{ scope.row.versionCode }}</span>
                            <span v-else>-/-</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="文件存放位置" prop="apkPath" width="180" show-overflow-tooltip
                        v-if="columns[7].visible" sortable>

                    </el-table-column>
                    <el-table-column label="上传时间" prop="createTime" align="center" width="150" show-overflow-tooltip
                        v-if="columns[5].visible" sortable />
                    <el-table-column label="描述" prop="description" align="center" show-overflow-tooltip
                        v-if="columns[6].visible" />
                    <el-table-column label="操作" align="center" fixed="right" width="140">
                        <template #default="scope">
                            <el-button link type="primary" icon="Edit" @click="handleApkEdit(scope.row)"  v-hasPerdisa="['file:disabled:edit']" v-hasPermi="['file:show:edit']">编辑</el-button>
                            <el-button link type="danger" icon="Delete" @click="handleApkDelete(scope.row)" v-hasPerdisa="['file:disabled:remove']" v-hasPermi="['file:show:remove']">删除</el-button>
                        </template>
                    </el-table-column>
                </el-table>
                <pagination v-show="total >= 0" :total="total" v-model:page="apkParams.pageNum"
                    v-model:limit="apkParams.pageSize" @pagination="getApkDataList" />
            </div>
        </div>
        <!-- 添加或修改分组对话框 -->
        <el-dialog :title="title" v-model="open" width="500px" append-to-body>
            <el-form ref="groupRef" :model="form" :rules="rules" label-width="80px">
                <el-form-item label="文件名" prop="apkName">
                    <el-input v-model="form.apkName" disabled />
                </el-form-item>
                <el-form-item label="包名" prop="apkPackage">
                    <el-input v-model="form.apkPackage" disabled />
                </el-form-item>
                <el-form-item label="描述" prop="description">
                    <el-input v-model="form.description" />
                </el-form-item>
                <!-- 文件存放路径 - 非APK文件时显示 -->
                <el-form-item v-if="!isEditingApkFile" label="存放路径" prop="apkPath" required>
                    <el-input
                        v-model="form.apkPath"
                        placeholder="存放路径 例：/sdcard/Download/com.wavelink.velocity"
                        clearable
                        :class="{ 'is-error': hasEditPathError }">
                    </el-input>
                    <div v-if="hasEditPathError" class="el-form-item__error">
                        请输入文件存放路径
                    </div>
                </el-form-item>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" @click="submitForm">确 定</el-button>
                    <el-button @click="cancel">取 消</el-button>
                </div>
            </template>
        </el-dialog>
        <!-- 用户导入对话框 -->
        <el-dialog :title="upload.title" v-model="upload.open" width="500px" append-to-body>
            <el-row class="mb10">
                <el-col>
                    <!-- <el-tree-select v-model="uploadPath" :data="fileList" @getCheckedNodes="(true,false)"  check-strictly :render-after-expand="false" @change="change" style="width: 240px" /> -->
                    <el-cascader v-model="uploadPath" :options="fileList" :props="{ checkStrictly: true }" />
                </el-col>
            </el-row>
            <el-upload ref="uploadRef" :limit="1"
                :headers="upload.headers" :action="upload.url" :disabled="upload.isUploading" :data="setFilePath"
                :on-progress="handleFileUploadProgress" :on-success="handleFileSuccess" :on-change="handleFileChange" :auto-upload="false" drag>
                <el-icon class="el-icon--upload"><upload-filled /></el-icon>
                <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em>
                    <div class="el-upload__tip">文件大小不超过100MB</div>
                </div>
            </el-upload>

            <!-- APK路径输入框 - 非APK文件时显示 -->
            <el-row v-if="showApkPathInput" style="margin-top: 15px;">
                <el-col>
                    <el-form-item label="存放路径：" label-width="94px" required>
                        <el-input
                            v-model="apkPath"
                            placeholder="存放路径 例：/sdcard/Download/com.wavelink.velocity"
                            clearable
                            :class="{ 'is-error': hasPathError }">
                        </el-input>
                        <div v-if="hasPathError" class="el-form-item__error">
                            请输入文件存放路径
                        </div>
                    </el-form-item>
                </el-col>
            </el-row>
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" @click="submitFileForm">确 定</el-button>
                    <el-button @click="upload.open = false">取 消</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script setup name="Group">
import { listGroup, getGroup, delGroup, addGroup, updateGroup, listDevice } from "@/api/group";
import { listApk, getFolderList, getApkList, addFolder, deleteItem, updateApk,getApk } from "@/api/apk.js";
import defaultImg from '@/assets/images/wenjian.png'
import showCloumn from "@/components/showColumn/index.vue";
import ApkDownloadNum from "@/components/ApkDownloadNum/index.vue";
import { ElMessageBox, ElMessage } from "element-plus";
import { nextTick, onMounted, ref, computed } from "vue";
import { handleTree } from "@/utils/handleData";
import { getToken } from "@/utils/auth";
const { proxy } = getCurrentInstance();
const props = ref({
    label: 'label'
});
const fileList = ref([]);
const fileCurrent = ref(null);
const fileCurrentPath = ref(null);
const fileFilterText = ref("");
const treeRef = ref(null);
const open = ref(false);
const loading = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const groupOptions = ref([]);
const refreshTable = ref(true); // 刷新表格
const data = reactive({
    form: {},
    queryParams: {
        groupName: null,
        parentId: null,
        parentIds: null,
        userName: null,
        password: null
    },
    rules: {
    }
});
const { queryParams, form, rules } = toRefs(data);
const apkList = ref([]); // 设备列表
const apkParams = ref({
    pageNum: 1,
    pageSize: 20,
    path: "\\filePath\\",
    apkName: null,
});
// 列显隐信息
const columns = ref([
    { key: 0, label: `文件名`, visible: true },
    { key: 1, label: `包名`, visible: true },
    { key: 2, label: `版本`, visible: true },
    { key: 3, label: `安装数量`, visible: true },
    { key: 4, label: `versionCode`, visible: true },
    { key: 5, label: `上传时间`, visible: true },
    { key: 6, label: `描述`, visible: true },
    { key: 7, label: `存放位置`, visible: true },
    // { key: 2, label: `文件地址`, visible: true },
]);
onMounted(() => {
});

nextTick(() => {
});


/** 查询文件夹列表 */
function getList() {
    // loading.value = true;
    getFolderList(queryParams.value).then(response => {
        let arr = handleTree(response.data[0])
        if (fileFilterText.value) {
            console.log(fileFilterText.value);
            const result = fileSearch(arr, fileFilterText.value)
            fileList.value = result;
            // return
        } else {
            fileList.value = arr;
        }
    });
}
/** 查询设备列表 */
function getApkDataList() {
    loading.value = true;
    if (fileCurrent.value) {
        getApkList(apkParams.value).then(response => {
            const lastPathName = apkParams.value.path
                ? apkParams.value.path.replace(/\\/g, '/').split('/').filter(Boolean).pop()
                : ''; // 获取最后一级文件夹名称
            apkList.value = response.rows.map(row => {
                if (row.filePath && lastPathName) {
                    const escapedName = lastPathName.replace(/[-/\\^$*+?.()|[\]{}]/g, '\\$&'); // 正则安全处理
                    const reg = new RegExp(escapedName, 'g');
                    if (reg.test(row.filePath)) {
                        row.filePath = row.filePath.replace(reg, `<span style="color: red">${lastPathName}</span>`);
                    }
                }
                return row;
            });
            total.value = response.total;
            loading.value = false;
        });
    } else {
        getApkList(apkParams.value).then(response => {
            apkList.value = JSON.parse(JSON.stringify(response.rows));
            total.value = response.total;
            loading.value = false;
        });
    }

}
// 文件夹搜索
function fileSearch(tree, value) {
    console.log(value);
    if (!Array.isArray(tree)) {
        tree = [tree];
    }
    let result = [];
    for (const node of tree) {
        let newNode = { ...node };
        if (newNode.children && newNode.children.length > 0) {
            newNode.children = fileSearch(newNode.children, value);
        } else {
            newNode.children = [];
        }
        // 当前节点匹配，或者有子节点匹配
        if ((newNode.label && newNode.label.includes(value)) || newNode.children.length > 0) {
            result.push(newNode);
        }
    }
    console.log(result);
    return result;
}
// 文件夹刷新
function fileRefresh() {
    fileCurrent.value = false;
    fileCurrentPath.value = null;
    fileFilterText.value = null;
    apkParams.value.path = "\\filePath\\"
    getList();
    getApkDataList();
}
// 文件夹点击事件
const handleNodeClick = (data, node, eNode) => {
    // 自己递归 parent 拼路径
    let pathNodes = [];
    let current = node;
    while (current) {
        if (!current.parent) break;
        pathNodes.unshift(current.data.label);
        current = current.parent;
    }
    apkParams.value.path = "\\" + pathNodes.join('\\') + "\\";
    fileCurrentPath.value = apkParams.value.path;
    fileCurrent.value = true;
    getApkDataList();
};
// 文件夹添加
const handleContextMenu = (data) => {
    // 自己递归 parent 拼路径
    let pathNodes = [];
    let current = data;
    while (current) {
        if (!current.parent) break; // 如果没有父节点了就退出循环
        pathNodes.unshift(current.data.label); // 把每一级的data（即label等信息）塞到前面
        current = current.parent;
    }
    ElMessageBox.prompt("请输入文件夹名称", "新建文件夹", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        inputPattern: /^[a-zA-Z0-9_]+$/,
        inputErrorMessage: "文件夹名称只能包含字母、数字和下划线",
    })
        .then(({ value }) => {
            if (value === "filePath") {
                ElMessage.error("文件夹名称不能为filePath");
                return;
            }
            let path = "/" + pathNodes.join('/') + "/" + value;
            addFolder(path).then(res => {
                ElMessage.success("新建成功");
                getList();
            });

        })
        .catch(() => {
            ElMessage.info("取消新建");
        });

};
// 文件夹删除
const handleFileDelete = (data) => {
    console.log('右键菜单', data);
    // 自己递归 parent 拼路径
    let pathNodes = [];
    let current = data;
    while (current) {
        if (!current.parent) break; // 如果没有父节点了就退出循环
        pathNodes.unshift(current.data.label); // 把每一级的data（即label等信息）塞到前面
        current = current.parent;
    }
    ElMessageBox.confirm(
        `确定要删除 ${data.label} 文件夹及其文件夹内所有内容 吗？`,
        "提示",
        {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
        }
    )
        .then(() => {
            let path = "/" + pathNodes.join('/') + "/";
            deleteItem({ path }).then(res => {
                // 这里执行删除操作
                ElMessage.success("删除成功");
                getList();
                getApkDataList();
            }, e => {
                ElMessage.error(e.message)
            })
        })
        .catch(() => {
            ElMessage.info("取消删除");
        });
}
// apk修改
const handleApkEdit = (data) => {
    open.value = true;
    title.value = "修改文件信息";
    getApk(data.id).then(res => {
        console.log(res.data);
        form.value = res.data;
    })
}
// apk删除
const handleApkDelete = (data) => {
    console.log(data);
    const { path, apkName } = formatPath(data.filePath);
    ElMessageBox.confirm(
        `确定要删除-- ${data.apkName} --文件吗？`,
        "提示",
        {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
        }
    )
        .then(() => {
            console.log(path, apkName);
            deleteItem({ path, apkName }).then(res => {
                // 这里执行删除操作
                ElMessage.success("删除成功");
                getList();
                getApkDataList();
            }, e => {
                ElMessage.error(e.message)
            })
        })
        .catch(() => {
            ElMessage.info("取消删除");
        });
}
// apk删除 格式化路径
function formatPath(pathStr) {
    let formatted = pathStr.replace(/\\/g, '/');
    const parts = formatted.split('/');
    const apkName = parts.pop();
    let path = parts.join('/');
    if (!path.startsWith('/')) path = '/' + path;
    if (!path.endsWith('/')) path = path + '/';
    return { path, apkName };
}

// 取消按钮
function cancel() {
    open.value = false;
    reset();
}
// 表单重置
function reset() {
    form.value = {
        id: null,
        groupName: null,
        parentId: null,
        parentIds: null,
        userName: null,
        password: null
    };
    proxy.resetForm("groupRef");
}
/** 修改按钮操作 */
function handleUpdate(row) {
    console.log(row);
    reset();
    getTreeselect();
    const _id = row.id || ids.value
    getGroup(_id).then(response => {
        form.value = response.data;
        open.value = true;
        title.value = "修改分组";
    });
}
/** 提交按钮 */
function submitForm() {
    proxy.$refs["groupRef"].validate(valid => {
        if (valid) {
            // 验证非APK文件的存放路径是否必填
            if (!isEditingApkFile.value && (!form.value.apkPath || form.value.apkPath.trim() === '')) {
                proxy.$modal.msgWarning("请输入文件存放路径");
                return;
            }

            console.log(form.value);
            updateApk(form.value).then(() => {
                proxy.$modal.msgSuccess("修改成功");
                open.value = false;
                getApkDataList() // 获取APK列表
            });
        }
    });
}
/** 删除按钮操作 */
function handleDelete(row) {
    const _ids = row.id || ids.value;
    proxy.$modal.confirm('是否确认删除分组编号为"' + _ids + '"的数据项？').then(function () {
        return delGroup(_ids);
    }).then(() => {
        getList();
        proxy.$modal.msgSuccess("删除成功");
    }).catch(() => { });
}

/*** 用户上传参数 */
const upload = reactive({
    // 是否显示弹出层（用户导入）
    open: false,
    // 弹出层标题（用户导入）
    title: "",
    // 是否禁用上传
    isUploading: false,
    // 是否更新已经存在的用户数据
    updateSupport: 0,
    // 设置上传的请求头部
    headers: { Authorization: "Bearer " + getToken() },
    // 上传的地址
    url: import.meta.env.VITE_APP_BASE_API + "/android/file/pushApk"
});
const uploadPath = ref(["filePath"]);
// APK路径相关
const apkPath = ref('');
const showApkPathInput = ref(false);
const currentFileName = ref('');

// 计算属性：检查路径是否有错误
const hasPathError = computed(() => {
    return showApkPathInput.value && (!apkPath.value || apkPath.value.trim() === '');
});

// 计算属性：检查编辑的文件是否为APK
const isEditingApkFile = computed(() => {
    if (!form.value.apkName) return false;
    const fileExtension = form.value.apkName.split('.').pop().toLowerCase();
    return fileExtension === 'apk';
});

// 计算属性：编辑时检查路径是否有错误
const hasEditPathError = computed(() => {
    return !isEditingApkFile.value && (!form.value.apkPath || form.value.apkPath.trim() === '');
});
/** 导入按钮操作 */
function handleImport() {
    upload.title = "上传资源";
    upload.open = true;
    // 重置状态
    showApkPathInput.value = false;
    apkPath.value = '';
    currentFileName.value = '';
};
/** 设置上传的文件路径 */
function setFilePath() {
    const path = uploadPath.value.join("\\");
    const data = { uploadPath: `\\${path}\\` };

    // 如果不是APK文件且有apkPath，则添加到上传数据中
    if (showApkPathInput.value && apkPath.value) {
        data.apkPath = apkPath.value;
    }

    return data;
};
/** 文件选择变化处理 */
function handleFileChange(file) {
    if (file && file.name) {
        currentFileName.value = file.name;
        const fileExtension = file.name.split('.').pop().toLowerCase();

        // 判断是否为APK文件
        if (fileExtension === 'apk') {
            // 是APK文件，隐藏输入框并清空值
            showApkPathInput.value = false;
            apkPath.value = '';
        } else {
            // 不是APK文件，显示输入框
            showApkPathInput.value = true;
        }
    }
}

/**文件上传中处理 */
const handleFileUploadProgress = () => {
    upload.isUploading = true;
};
/** 文件上传成功处理 */
const handleFileSuccess = (response, file) => {
    upload.open = false;
    upload.isUploading = false;
    proxy.$refs["uploadRef"].handleRemove(file);
    proxy.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + "</div>", "上传结果", { dangerouslyUseHTMLString: true });
    getList();
    getApkDataList();
};
/** 提交上传文件 */
function submitFileForm() {
    // 验证非APK文件的存放路径是否必填
    if (showApkPathInput.value && (!apkPath.value || apkPath.value.trim() === '')) {
        proxy.$modal.msgWarning("请输入文件存放路径");
        return;
    }

    proxy.$refs["uploadRef"].submit();
};



getList(); // 获取文件夹列表
getApkDataList() // 获取APK列表
// checkDivid
const leftShow = ref(true);
const rightShow = ref(true);
function checkDivid(type) {
    if (type === "left") {
        leftShow.value = false;
    } else if (type === "right") {
        leftShow.value = true;
    }
}
</script>
<style lang="scss" scoped>
.group {
    padding: 10px !important;
    display: flex;
    flex-direction: row;
    background-color: #f5f5f5 !important;

    .menu {
        width: 250px;
        height: 100%;
        // overflow: auto;
        background-color: #fff;
        border: 1px solid #e4e7ed;
        padding: 8px;
        box-sizing: border-box;
        border-radius: 8px;
        display: flex;
        flex-direction: column;
        position: relative;
        transition: all 0.3s ease-in-out;
        .divid {
            position: absolute;
            top: 0;
            bottom: 0;
            right: 0;
            margin: auto;
            width: 10px;
            height: 20px;
            background-color: #e4e7ed;
            border-radius: 4px 0 0 4px;
            transition: all 0.3s ease;
        }

        .el-button {
            padding: 12px !important;
        }

        .el-divider {
            margin: 10px 0 !important;
        }

        .menu-tree {
            flex: 1;
            overflow: auto;
            scrollbar-width: none;
            /* Firefox */
            -ms-overflow-style: none;

            /* IE 10+ */
            ::-webkit-scrollbar {
                display: none;
                /* Chrome, Safari, Opera */
            }

            .menu-tree-item {
                width: 100%;
                display: flex;
                align-items: center;
                justify-content: space-between;
                padding: 0px 10px 0px 0px;

                .el-button {
                    padding: 4px !important;
                    margin-left: 0px !important;
                }

                .btn-group {
                    display: flex;
                    gap: 2px;
                }
            }
        }
    }

    .content {
        flex: 1;
        overflow: hidden;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        margin-left: 10px;
        position: relative;
        transition: all 0.3s ease-in-out;
        .divid {
            position: absolute;
            top: 0;
            bottom: 0;
            left: 0;
            margin: auto;
            width: 10px;
            height: 20px;
            background-color: #e4e7ed;
            border-radius:0 4px 4px 0;
            transition: all 0.3s ease;
        }

        .content_top {
            // height: 80px;
            width: 100%;
            background-color: #fff;
            border-radius: 8px;
            box-sizing: border-box;
            margin-bottom: 10px;
            border: 1px solid #e4e7ed;
            padding:11px;
            box-sizing: border-box;

            .el-button {
                padding: 12px !important;
            }

        }

        .content_bottom {
            flex: 1;
            width: 100%;
            background-color: #fff;
            border-radius: 8px;
            box-sizing: border-box;
            border: 1px solid #e4e7ed;
            padding: 10px;
        }
    }
    .divid {
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 12px;
        color: #722ed1;
    }

    .divid:hover {
        transform: scale(1.2);
    }

    .lf-table-content-height {
        height: calc(100vh - 235px) !important;
    }

    :deep(.el-table__inner-wrapper:before) {
        height: 0.8px !important;
    }

    // 输入框的margin-bottom
    :deep(.el-form-item) {
        margin-bottom: 8px !important;
    }

    :deep(.el-drawer__header) {
        margin-bottom: 5px;
    }

    :deep(.el-drawer__body) {
        padding: 0px;
    }

    .remote-content {
        width: 100%;
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        background-color: #f5f5f5;

        #imageCanvas {
            display: block;
            height: 100%;
            width: auto;
            touch-action: none;
        }

        #imageElement {
            height: 100%;
            // object-fit: cover;
        }
    }

    // 错误状态样式
    .is-error :deep(.el-input__wrapper) {
        border-color: #f56c6c;
        box-shadow: 0 0 0 1px #f56c6c inset;
    }

    .is-error :deep(.el-input__wrapper:hover) {
        border-color: #f56c6c;
        box-shadow: 0 0 0 1px #f56c6c inset;
    }

    .el-form-item__error {
        color: #f56c6c;
        font-size: 12px;
        line-height: 1;
        padding-top: 4px;
        position: absolute;
        top: 100%;
        left: 0;
    }
}
</style>