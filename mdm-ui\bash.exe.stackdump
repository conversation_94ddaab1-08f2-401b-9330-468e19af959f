Stack trace:
Frame         Function      Args
0007FFFF9DD0  00021005FEBA (000210285F48, 00021026AB6E, 000000000000, 0007FFFF8CD0) msys-2.0.dll+0x1FEBA
0007FFFF9DD0  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFA0A8) msys-2.0.dll+0x67F9
0007FFFF9DD0  000210046832 (000210285FF9, 0007FFFF9C88, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFF9DD0  000210068F86 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28F86
0007FFFF9DD0  0002100690B4 (0007FFFF9DE0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x290B4
0007FFFFA0B0  00021006A49D (0007FFFF9DE0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A49D
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFDD1EE0000 ntdll.dll
7FFDD13F0000 KERNEL32.DLL
7FFDCF0D0000 KERNELBASE.dll
7FFDD0BB0000 USER32.dll
7FFDCFC50000 win32u.dll
7FFDD15D0000 GDI32.dll
7FFDCF750000 gdi32full.dll
7FFDCF6A0000 msvcp_win.dll
7FFDCF550000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFDD0AF0000 advapi32.dll
7FFDD1DD0000 msvcrt.dll
7FFDD1B20000 sechost.dll
7FFDCFE80000 RPCRT4.dll
7FFDCE630000 CRYPTBASE.DLL
7FFDCF030000 bcryptPrimitives.dll
7FFDCFD10000 IMM32.DLL
