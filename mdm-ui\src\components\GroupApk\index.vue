<template>
    <el-tooltip content="点击后呈现当前所选组的策略" placement="top" effect="dark">
        <el-button type="success" text bg size="large" icon="Tools" @click="getDeviceList" v-hasPerdisa="['group:disabled:strategy']" v-hasPermi="['group:show:strategy']">策略</el-button>
    </el-tooltip>
    <el-drawer v-model="open" title="设置组策略" size="100%" direction="rtl" @close="handleClose">
        <div class="remote-content">
            <el-transfer v-model="value" :data="apkList" :props="prop"
                :titles="['所有资源', (groupCurrent || '全部') + ' 组策略']" :button-texts="['移除', '添加']" @change="handleChange">
                <template #default="{ option }">
                    <div class="item">
                        <div style="display: flex; justify-content: center; align-items: center; padding: 0 10px;">
                            <img :src="`http://${option.apkImage}`" alt="" style="width: 28px; height: 28px;" />
                        </div>
                        <div><span>{{ option.apkName }} - {{ option.apkPackage }}</span></div>
                    </div>
                </template>
                <template #left-empty>
                    <el-empty :image-size="60" description="No data" />
                </template>
                <template #right-empty>
                    <el-empty :image-size="60" description="No data" />
                </template>
            </el-transfer>
        </div>
    </el-drawer>
</template>
<script setup>
import { defineProps, ref } from 'vue'
import { getGroupApk, addGroupApk } from '@/api/group_apk'
import { getApkList } from "@/api/apk.js";
import { ElNotification } from 'element-plus'
const props = defineProps({
    groupCurrentId: {
        type: String,
        default: "1"
    },
    groupCurrent: {
        type: String,
        default: "全部"
    }
})
const open = ref(false)
const value = ref([])
const apkList = ref([])
const apkParams = ref({
    pageNum: 1,
    pageSize: 10000,
    path: "\\filePath\\",
    apkName: null,
});
const prop = {
    key: 'id',
    label: 'apkName',
    disabled: false,
}
function getDeviceList() {
    open.value = true
    getList()
    getApkDataList()
}
// 获取组策略列表
function getList() {
    getGroupApk(props.groupCurrentId || "1").then((res) => {
        value.value = res.data.map(item => item.apkId);
    });
}
// 获取所有apk列表
function getApkDataList() {
    getApkList(apkParams.value).then(response => {
        apkList.value = JSON.parse(JSON.stringify(response.rows));
    });
}
function handleChange() {
    addGroupApk({
        groupId: props.groupCurrentId || "1",
        apkIdList: value.value
    }).then(res => {
        console.log(res);
        // open.value = false
        ElNotification({
            title: '操作成功',
            message: res.msg,
            type: 'success',
            position: 'top-left',
        })
    }, err => {
        ElNotification({
            title: '失败',
            message: err.msg,
            type: 'error',
            position: 'top-left',
        })
    })
}
function handleClose() {
    open.value = false
    value.value = []
    apkList.value = []
}
</script>
<style lang="scss" scoped>
.remote-content {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #f5f5f5;

    .item {
        width: 100%;
        height: 100%;
        display: flex;
    }
}

:deep(.el-transfer-panel) {
    width: 700px !important;
    height: 500px !important;
}

:deep(.el-transfer-panel__body) {
    height: calc(100% - 40px) !important;
}

:deep(.el-transfer-panel__list) {
    height: 100% !important;
}
</style>