import request from '@/utils/request'
export  function pushApk() {
    return request({
        url: '/android/push/apk',
        method: 'post'
    })
}


export function deleteItem(data) {
return request({
    url: `/android/push/deleteApk`,
    method: 'post',
    params: {
        path: data.path,
        apkName: data.apkName
    }
})
}

export function getFolderList(data) {
    return request({
        url: `/android/file/getFolderList`,
        method: 'get'
    })
}

// 新建文件夹
export function addFolder(folderPath) {
    return request({
        url: `/android/file/createFolder`,
        method: 'post',
        params: {
            folderPath // /failPath/123
        }
    })
}


// apkList
export function getApkList(params) {
    return request({
        url: '/android/file/getApkListByPath',
        method: 'get',
        params: params
    })
}

// 获取apk下载设备
export function getApkDownloadDevice(data) {
  return request({
    url: '/android/file/getInstallDevice',
    method: 'get',
    params: {
      packageName: data.apkPackage,
      versionName: data.version,
      pageSize: data.pageSize,
      pageNum: data.pageNum
    }
  })
}

// 同步本地数据
export function syncFile() {
  return request({
    url: '/android/file/synchronizeServerFiles',
    method: 'post'
  })
}















































// 查询apk信息列表
export function listApk(query) {
  return request({
    url: '/pc/apk/list',
    method: 'get',
    params: query
  })
}

// 查询apk信息详细
export function getApk(id) {
  return request({
    url: '/pc/apk/' + id,
    method: 'get'
  })
}

// 新增apk信息
export function addApk(data) {
  return request({
    url: '/pc/apk',
    method: 'post',
    data: data
  })
}

// 修改apk信息
export function updateApk(data) {
  return request({
    url: '/pc/apk',
    method: 'put',
    data: data
  })
}

// 删除apk信息
export function delApk(id) {
  return request({
    url: '/pc/apk/' + id,
    method: 'delete'
  })
}

