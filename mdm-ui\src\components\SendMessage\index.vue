<template>
    <el-button type="primary" text :underline="false" @click="handleClick()" size="small"
        v-hasPerdisa="['group:disabled:sendMessage']" v-hasPermi="['group:show:sendMessage']"
        :disabled="row.status == 2">发送消息</el-button>
    <el-dialog v-model="open" title="发送消息" width="500px" append-to-body>
        <el-form ref="form" label-width="80px">
            <el-form-item label="消息内容">
                <el-input v-model="message" placeholder="请输入消息内容" />
            </el-form-item>
        </el-form>
        <template #footer>
            <el-button @click="handleClose">取消</el-button>
            <el-button type="success" @click="handleConfirm">发送</el-button>
        </template>
    </el-dialog>
</template>
<script setup>
import { defineProps } from 'vue'
import { ElNotification } from 'element-plus';
import { pcToServerControlDevice } from "@/api/pc/pcToServerControlDevice.js";
import { addLog } from "@/api/log/operate.js";
import { reactive } from 'vue'
const props = defineProps({
    row: {
        type: Object,
        default: () => ({})
    }
})
const open = ref(false)
const message = ref('');
const webSocket = ref(null);
function handleClick() {
    open.value = true
}
function handleClose() {
    open.value = false
    message.value = ''
}

// 用户直接通过webSocket和设备通信
// function handleConfirm() {
//     if (webSocket.value) {
//         webSocket.value.close()
//         webSocket.value = null
//     }
//     webSocket.value = new WebSocket(`ws://${props.row.ip}:5008/`);
//     webSocket.value.onopen = function () {
//         webSocket.value.send(JSON.stringify([{
//             type: "cellPhone",
//             value: message.value
//         }]))
//         ElNotification({
//             title: '提示',
//             message: '发送成功',
//             type: 'success',
//         })
//         webSocket.value.close()
//         webSocket.value = null
//         handleClose()
//         addLog({
//                 type: "控制指令",
//                 subdivisionType: "发送消息",
//                 isSuccess: "成功",
//                 deviceId: props.row.id,
//                 content: message.value
//             }).then(res => {
//                 console.log(res);
//                 message.value = null
//             });


//     }
//     webSocket.value.onmessage = function (event) {
//         const res = JSON.parse(event.data);
//         console.log(res);
//     }
// }

// 通过服务器转发
function handleConfirm() {
    pcToServerControlDevice({
        array: [{
            deviceId: props.row.id,
            ip: props.row.ip,
            content: JSON.stringify(
                [{
                    type: "cellPhone",
                    value: message.value
                }]),
            type: "控制指令",
            subdivisionType: "发送消息"
        }]
    }).then(res => {
        console.log(res);
        ElNotification({
            title: '发送指令',
            message: "成功发送",
            type: 'success',
            position: 'top-left',
        });

    }).catch(err => {
        ElNotification({
            title: '发送指令',
            message: "发送失败",
            type: 'error',
            position: 'top-left',
        });

    }).finally(() => {
        handleClose()
    });
}

</script>
<style lang="scss" scoped></style>