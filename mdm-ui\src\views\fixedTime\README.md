# FixedTime 定时推送页面优化

## 🎯 需求实现

### ✅ 需求1：修改新增时所有推送设备为选择形式
- **先选择组**：通过下拉框选择设备组
- **再选择设备**：选择组后显示该组下的所有设备
- **同组限制**：所选设备必须为同一组（通过组选择实现）
- **设备信息**：显示设备名称、IP地址、MAC地址
- **多选支持**：可选择多个设备，实时显示选择数量

### ✅ 需求2：修改新增时选中的所有APK为选择形式（关联设备组）
- **自动关联**：选择设备组后，APK区域自动显示该组绑定的APK策略
- **组信息显示**：显示当前选择的设备组信息和已选设备数量
- **APK信息**：显示APK图标、名称、包名
- **多选支持**：可选择多个APK，实时显示选择数量
- **数据一致性**：确保设备和APK来自同一组，保持数据关联性

### ✅ 需求3：优化时间选择器
- **分离式选择**：日期选择器 + 时间下拉框
- **30分钟间隔**：时间选项每30分钟一个（00:00, 00:30, 01:00...）
- **易于操作**：避免在时间选择器中难以选择30分钟的问题

### ✅ 需求4：添加复制新增功能
- **一键复制**：点击复制按钮快速创建相似任务
- **智能填充**：自动填充设备组、设备选择、APK选择、覆盖选项、备注等信息
- **时间重置**：推送时间需要重新选择（避免重复执行）
- **组关联恢复**：自动识别设备所属组并恢复选择状态

### ✅ 需求5：优化选择框显示和交互
- **固定高度**：设备和APK选择框固定300px高度，支持滚动
- **全选功能**：添加全选/取消全选功能，支持半选状态显示
- **数量显示**：实时显示已选择数量和总数量（如：3/10）
- **滚动优化**：自定义滚动条样式，提升视觉体验
- **批量操作**：支持快速全选所有设备或APK

### ✅ 需求6：界面布局重构
- **抽屉式界面**：将对话框改为抽屉（el-drawer），提供更大的操作空间
- **左右布局**：设备选择和APK选择改为左右并排布局，提高空间利用率
- **上下标签**：所有表单标签改为上下结构，更清晰的视觉层次
- **固定按钮**：操作按钮固定在底部，无需滚动即可操作
- **响应式高度**：选择区域自适应抽屉高度，最大化显示内容

### ✅ 需求7：数据回显修复（使用专门API）
- **修改功能回显**：使用 `getDeviceByJobId` 和 `getApkByJobId` API获取准确的任务关联数据
- **复制功能回显**：同样使用专门API确保数据准确性
- **时间格式处理**：正确解析"2025-07-03 17:30:00"格式的时间数据
- **设备组关联**：通过任务设备信息直接获取组ID，避免遍历查找
- **精度问题解决**：使用专门API避免了大整数精度丢失问题

### ✅ 需求8：表格显示优化（使用专门API）
- **设备查看按钮**：表格中"所选设备"列改为按钮，显示设备数量
- **APK查看按钮**：表格中"推送APK"列改为按钮，显示APK数量
- **设备详情对话框**：使用 `getDeviceByJobId` API直接获取任务设备详情
- **APK详情对话框**：使用 `getApkByJobId` API直接获取任务APK详情
- **数量统计**：按钮文本显示选择的数量，如"查看设备 (3)"
- **性能优化**：避免遍历所有组查找设备/APK，直接通过任务ID获取

### 🔧 API接口使用
- **getDeviceByJobId(jobId)**：获取任务关联的设备信息，返回包含设备详情和组信息的数组
- **getApkByJobId(jobId)**：获取任务关联的APK信息，返回包含APK详情的数组
- **listGroup()**：获取设备组列表
- **listDevice({groupId})**：获取指定组的设备列表
- **getGroupApk(groupId)**：获取指定组的APK列表

## 修改内容

### ✅ 代码风格统一
按照 `log/heartbeatLog` 的代码风格进行了全面改造：

1. **容器类名**：`app-container` → `lf-content`
2. **表格样式**：添加了 `border`、`size="small"`、`lf-table-content-height` 类
3. **表格列**：添加了 `show-overflow-tooltip`、`sortable` 属性
4. **分页大小**：`pageSize: 10` → `pageSize: 20`
5. **标签宽度**：统一为 `80px`

### ✅ 要求1：是否覆盖原APP改为下拉框

**搜索表单中：**
```vue
<el-form-item label="是否覆盖原APP" prop="checkedPackage" style="width: 260px;">
    <el-select v-model="queryParams.checkedPackage" placeholder="请选择是否覆盖原APP" clearable @change="handleQuery">
        <el-option label="不覆盖" value="0" />
        <el-option label="覆盖" value="1" />
    </el-select>
</el-form-item>
```

**对话框表单中：**
```vue
<el-form-item label="是否覆盖原APP" prop="checkedPackage">
    <el-select v-model="form.checkedPackage" placeholder="请选择是否覆盖原APP" style="width: 100%;">
        <el-option label="不覆盖" value="0" />
        <el-option label="覆盖" value="1" />
    </el-select>
</el-form-item>
```

**表格显示优化：**
```vue
<el-table-column label="是否覆盖原APP" align="center" prop="checkedPackage" width="120" show-overflow-tooltip>
    <template #default="scope">
        <span>{{ scope.row.checkedPackage === 1 ? '覆盖' : (scope.row.checkedPackage === 0 ? '不覆盖' : '未知') }}</span>
    </template>
</el-table-column>
```

### ✅ 要求2：新增定时任务推送时间改为带时分并且步长为30min

**时间选择器配置：**
```vue
<el-form-item label="定时任务推送时间" prop="pushTime">
    <el-date-picker 
        clearable 
        v-model="form.pushTime" 
        type="datetime" 
        value-format="YYYY-MM-DD HH:mm:ss"
        format="YYYY-MM-DD HH:mm"
        placeholder="请选择推送时间"
        :disabled-date="disabledDate"
        :disabled-minutes="disabledMinutes"
        style="width: 100%;">
    </el-date-picker>
</el-form-item>
```

**时间限制函数：**
```javascript
// 禁用过去的日期
const disabledDate = (time) => {
    return time.getTime() < Date.now() - 8.64e7; // 禁用昨天之前的日期
};

// 禁用分钟，只允许00和30分钟
const disabledMinutes = () => {
    const minutes = [];
    for (let i = 0; i < 60; i++) {
        if (i !== 0 && i !== 30) {
            minutes.push(i);
        }
    }
    return minutes;
};
```

**表格时间显示：**
```vue
<el-table-column label="推送时间" align="center" prop="pushTime" width="180" show-overflow-tooltip sortable>
    <template #default="scope">
        <span>{{ parseTime(scope.row.pushTime, '{y}-{m}-{d} {h}:{i}') }}</span>
    </template>
</el-table-column>
```

### ✅ 其他优化

1. **表单验证规则**：
   - 推送时间必填
   - 设备ID必填
   - APK ID必填
   - 是否覆盖原APP必选

2. **表格状态显示**：
   - 推送状态：0待执行、1已执行、2执行失败
   - 是否覆盖：0不覆盖、1覆盖

3. **样式统一**：
   - 添加了 `lf-table-content-height` 样式
   - 表格边框和间距优化
   - 响应式布局

## 🔧 技术实现

### 数据回显机制

**修改功能数据回显：**
```javascript
async function handleUpdate(row) {
    reset();
    await loadGroupList();
    generateTimeOptions();

    const _id = row.id || ids.value
    getJob(_id).then(response => {
        form.value = response.data;

        // 回显时间数据
        if (response.data.pushTime) {
            const pushTime = response.data.pushTime;
            const [datePart, timePart] = pushTime.split(' ');
            selectedDate.value = datePart;
            selectedTime.value = timePart.substring(0, 5); // 去掉秒数
        }

        // 回显设备数据
        if (response.data.deviceIds) {
            const deviceIdArray = response.data.deviceIds.split(',').map(id => parseInt(id));
            selectedDevices.value = deviceIdArray;
            loadDeviceGroupFromDeviceIds(deviceIdArray);
        }

        // 回显APK数据
        if (response.data.apkIds) {
            const apkIdArray = response.data.apkIds.split(',').map(id => parseInt(id));
            selectedApks.value = apkIdArray;
        }

        open.value = true;
        title.value = "修改定时推送";
    });
}
```

### 表格详情查看

**设备详情查看：**
```javascript
async function showDeviceDetails(row) {
    if (!row.deviceIds) {
        proxy.$modal.msgWarning("该任务没有选择设备");
        return;
    }

    try {
        const deviceIds = row.deviceIds.split(',').map(id => parseInt(id));
        deviceDetailList.value = [];

        const groupResponse = await listGroup();
        const groups = groupResponse.data || groupResponse;

        for (const group of groups) {
            const deviceResponse = await listDevice({ groupId: group.id });
            const devices = deviceResponse.rows || deviceResponse.data || [];

            const matchingDevices = devices.filter(device => deviceIds.includes(device.id));
            matchingDevices.forEach(device => {
                deviceDetailList.value.push({
                    ...device,
                    groupName: group.groupName
                });
            });
        }

        deviceDetailOpen.value = true;
    } catch (error) {
        console.error('获取设备详情失败:', error);
        proxy.$modal.msgError("获取设备详情失败");
    }
}
```

**表格列定义：**
```vue
<el-table-column label="所选设备" align="center" width="150" show-overflow-tooltip>
    <template #default="scope">
        <el-button link type="primary" @click="showDeviceDetails(scope.row)">
            查看设备 ({{ scope.row.deviceIds ? scope.row.deviceIds.split(',').length : 0 }})
        </el-button>
    </template>
</el-table-column>
```

### API接口集成
```javascript
import { listGroup, listDevice } from "@/api/group";
import { getGroupApk } from "@/api/group_apk";
import { listApk } from "@/api/apk";
```

### 核心功能函数
- `loadGroupList()`: 加载组列表
- `handleDeviceGroupChange()`: 设备组选择变化处理
- `loadDeviceList()`: 根据组ID加载设备列表
- `loadApkList()`: 根据组ID加载APK策略列表
- `handleDeviceSelectionChange()`: 设备选择变化处理
- `handleApkSelectionChange()`: APK选择变化处理
- `handleDeviceSelectAll()`: 设备全选/取消全选处理
- `handleApkSelectAll()`: APK全选/取消全选处理
- `updateDeviceSelectAllStatus()`: 更新设备全选状态
- `updateApkSelectAllStatus()`: 更新APK全选状态
- `handleCopy()`: 复制任务功能
- `loadDeviceGroupFromDeviceIds()`: 根据设备ID反向查找组

### 数据流转
1. **组选择** → **加载对应数据** → **用户多选** → **更新表单字段**
2. **设备选择**: `selectedDevices` → `form.deviceIds` (逗号分隔)
3. **APK选择**: `selectedApks` → `form.apkIds` (逗号分隔)

### 响应式数据管理
```javascript
// 组和数据列表
const groupList = ref([]);
const deviceList = ref([]);
const apkList = ref([]);

// 加载状态
const deviceLoading = ref(false);
const apkLoading = ref(false);

// 选择状态
const selectedDeviceGroupId = ref(null);
const selectedDevices = ref([]);
const selectedApks = ref([]);

// 全选状态
const deviceSelectAll = ref(false);
const deviceIndeterminate = ref(false);
const apkSelectAll = ref(false);
const apkIndeterminate = ref(false);

// 时间选择
const selectedDate = ref('');
const selectedTime = ref('');
const timeOptions = ref([]);
```

## 功能特点

- **抽屉式界面**：80%屏幕宽度的抽屉，提供充足的操作空间
- **时间限制**：只能选择今天及以后的日期
- **分离式时间选择**：日期选择器 + 时间下拉框，30分钟间隔
- **组织化选择**：先选组再选具体项目，确保数据一致性
- **左右并排布局**：设备和APK选择左右分布，最大化空间利用
- **批量操作**：支持全选/取消全选，半选状态智能显示
- **自适应高度**：选择区域自适应抽屉高度，最大化显示内容
- **实时反馈**：显示选择数量和状态（如：已选3/总共10）
- **智能复制**：一键复制任务，自动恢复选择状态
- **固定操作按钮**：底部固定按钮，无需滚动即可操作
- **上下标签结构**：清晰的视觉层次和信息组织
- **数据验证**：完善的表单验证规则
- **错误处理**：图片加载失败、API调用异常等

## 使用说明

### 新增定时任务流程

1. **点击新增按钮**：打开定时推送抽屉（右侧滑出）
2. **选择推送时间**：
   - 日期选择器：选择推送日期
   - 时间下拉框：选择具体时间（30分钟间隔）
3. **选择设备组**：
   - 从下拉框选择设备组
   - 显示已选择组的信息
4. **选择推送设备和APK**（左右布局）：
   - **左侧设备面板**：显示该组下的所有设备，支持全选和多选
   - **右侧APK面板**：自动显示该组绑定的APK策略，支持全选和多选
   - 实时显示选择数量（如：已选3/总共10）
   - 大尺寸选择区域，支持滚动浏览
5. **选择覆盖选项**：是否覆盖原APP
6. **填写备注**：可选
7. **确认提交**：底部固定按钮，无需滚动

### 复制新增流程

1. **点击复制按钮**：在表格操作列点击复制按钮
2. **自动填充信息**：
   - 自动识别并选择设备所属组
   - 恢复设备选择状态
   - 恢复APK选择状态
   - 复制覆盖选项和备注
3. **重新选择时间**：推送时间需要重新设置
4. **确认提交**：检查信息后提交新任务

### 搜索过滤

- **按推送时间筛选**：选择具体日期
- **按覆盖选项筛选**：不覆盖/覆盖
- **重置搜索**：清空所有筛选条件

### 状态查看

- **推送状态**：0待执行、1已执行、2执行失败
- **覆盖状态**：不覆盖/覆盖
- **时间显示**：完整的日期时间格式
- **设备和APK信息**：显示选择的设备ID和APK ID
