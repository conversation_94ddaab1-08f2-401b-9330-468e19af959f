<template>
    <div class="wifi-signal">
      <div class="bars">
        <div
          v-for="n in 4"
          :key="n"
          :class="['bar', { active: n <= signalLevel }]"
          :style="{ height: `${n * 4}px` }"
        ></div>
      </div>
      <span class="rssi">
        {{ isEmpty ? '无' : `${rssi}dB` }}
      </span>
    </div>
  </template>
  
  <script setup>
  import { defineProps, computed } from 'vue'
  
  const props = defineProps({
    rssi: {
      type: [Number, String],
      default: null,
    },
  })
  
  // 判断 rssi 是否为空
  const isEmpty = computed(() => {
    return props.rssi === null || props.rssi === undefined || props.rssi === ''
  })
  
  // 信号等级计算（0~4）
  const signalLevel = computed(() => {
    if (isEmpty.value) return 0
    const rssi = Number(props.rssi)
    if (rssi >= -50) return 4
    else if (rssi >= -70) return 3
    else if (rssi >= -80) return 2
    else return 1
  })
  </script>
  
  <style scoped lang="scss">
  .wifi-signal {
    display: flex;
    align-items: flex-end;
    gap: 4px;
    font-size: 12px;
    color: #333;
  
    .bars {
      display: flex;
      align-items: flex-end;
      height: 20px;
      gap: 1px;
    }
  
    .bar {
      width: 3px;
      background-color: #ccc; // 默认灰色
      transition: background-color 0.3s;
  
      &.active {
        background-color: #4caf50; // 信号激活颜色
      }
    }
  
    .rssi {
    line-height: 15px;
    color: #999;
      
    }
  }
  </style>
  