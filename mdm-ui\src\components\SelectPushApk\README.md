# SelectPushApk 组件改进

## 📋 功能概述

SelectPushApk 组件用于向选中的设备推送APK，现已支持按设备分组进行批量推送操作。

## ✅ 按分组推送的改进

### 🎯 核心功能

**1. 设备分组分类**
```javascript
// 按分组ID对设备进行分类的函数
function groupDevicesByGroupId(devices) {
    const grouped = {};
    devices.forEach(device => {
        const groupId = device.groupId;
        if (!grouped[groupId]) {
            grouped[groupId] = [];
        }
        grouped[groupId].push(device);
    });
    return grouped;
}
```

**2. 分组推送逻辑**
```javascript
// 获取所有设备ID，调用一次推送接口
const allDeviceIds = data.value.map(device => device.id);
console.log('所有设备ID数组:', allDeviceIds);

// 调用推送接口（只调用一次，传递所有设备）
sendMessageToAndroid({
    deviceIds: allDeviceIds,
    type: "server",
    value: value,
    checkedPackage: checkedPackage.value,
    isAllGroup: true
}).then(res => {
    console.log('推送结果:', res);
});

// 按分组对设备进行分类
const groupedDevices = groupDevicesByGroupId(data.value);
console.log('按分组分类的设备:', groupedDevices);

// 为每个分组分别调用 addPushLog 接口
Object.entries(groupedDevices).forEach(([groupId, devices]) => {
    const deviceIds = devices.map(device => device.id);

    // 为每个分组调用 addPushLog 接口
    addPushLog({
        deviceIds: deviceIds.join(","),
        groupId: groupId,
        apkIds: ""
    });
});
```

### 🔧 数据处理

**输入数据格式**
```json
[
    {
        "id": "1939494314749947906",
        "groupId": "1940614548571463682",
        "groupName": "123",
        "deviceName": "TC26",
        "sn": "23059523021147"
    },
    {
        "id": "1942520050868006913",
        "groupId": "1940614548571463682", 
        "groupName": "123",
        "deviceName": "MC3300ax",
        "sn": "22114523021397"
    },
    {
        "id": "1942845646810624001",
        "groupId": "1941234567890123456",
        "groupName": "456", 
        "deviceName": "CRUISE KC",
        "sn": "C800I3566000"
    }
]
```

**分组后的数据结构**
```javascript
{
    "1940614548571463682": [
        { id: "1939494314749947906", groupId: "1940614548571463682", ... },
        { id: "1942520050868006913", groupId: "1940614548571463682", ... }
    ],
    "1941234567890123456": [
        { id: "1942845646810624001", groupId: "1941234567890123456", ... }
    ]
}
```

### 📊 API调用

**1. sendMessageToAndroid 接口**
- **只调用一次**：传递所有选中设备的ID
- 统一推送：所有设备一次性推送
- 保持原有的推送逻辑和效率

**2. addPushLog 接口**
- **按分组循环调用**：为每个分组创建独立的推送日志
- 包含参数：
  - `deviceIds`: 该分组设备ID的逗号分隔字符串
  - `groupId`: 分组ID
  - `apkIds`: APK ID列表（当前为空字符串）

### 🎯 业务逻辑

**分组推送的优势：**
1. **高效推送**：所有设备一次性推送，提高效率
2. **精确日志**：按分组记录推送日志，便于管理
3. **独立追踪**：可以按分组追踪推送状态
4. **日志清晰**：推送日志按分组记录，便于查看和分析

**执行流程：**
1. 接收选中的设备列表
2. 提取所有设备ID，调用一次推送接口
3. 按 `groupId` 对设备进行分组
4. 遍历每个分组，为每个分组记录推送日志
5. 显示统一的成功通知

### 📝 日志记录

每个分组会生成独立的推送日志记录：
- **设备列表**：该分组内所有设备的ID
- **分组信息**：记录具体的分组ID
- **推送状态**：独立追踪每个分组的推送结果

### 🔄 错误处理

- **分组验证**：确保每个设备都有有效的 `groupId`
- **接口调用**：每个分组的接口调用独立处理
- **日志记录**：推送和日志记录分别处理错误

## 🎯 使用场景

适用于以下场景：
- 多个分组的设备需要推送相同的APK
- 需要按分组管理推送任务
- 需要独立追踪每个分组的推送状态
- 需要按分组查看推送日志
