<template>
  <el-button type="primary" @click="initWebSocket">获取设备位置信息</el-button>
  <div class="lf-content electronicFence" ref="canvas"></div>
</template>
<script setup>
import { ElNotification } from 'element-plus';
import * as echarts from "echarts/core";
import { TooltipComponent, GridComponent, MarkAreaComponent } from "echarts/components";
import { ScatterChart } from "echarts/charts";
import { CanvasRenderer } from "echarts/renderers";
echarts.use([TooltipComponent, GridComponent, ScatterChart, CanvasRenderer, MarkAreaComponent]);
import {handleIphoneLocation} from "@/utils/rssiToMeter";
const option = ref();
const myChart = ref(null);
const canvas = ref(null);
const socket = ref(null);
const apList = ref([
  // [x,y,ssid,信号发射功率1m的, 衰减强度, heihgt]
  [0, 0, 'sprt-room', -17, 3, 1.8],
  [-2.5, -3.5, 'sprt-room1', -32, 3, 0.2],
  [-2.2, -4.5, 'sprt-room2', -25, 3, 0.6]
]);
const iphoneList = ref([
  // [-2, 2, "*************", 1],
]);



onMounted(() => {
  echartInit();
  option.value && myChart.value.setOption(option.value);

});
function echartInit() {
  myChart.value = echarts.init(canvas.value);

  option.value = {
    tooltip: {
      position: 'top',
      formatter: (params) => {
        const [x, y, ip] = params.data;
        return `坐标: (${x.toFixed(2)}米, ${y.toFixed(2)}米)<br>设备: ${ip ?? "未知"}`;
      }
    },
    grid: {
      left: 120
    },
    xAxis: {
      min: -8,
      max: 8,
      axisLine: { show: false },
      axisTick: { show: false },
      axisLabel: { show: true, formatter: '{value} 米' },
      splitLine: { show: true }
    },
    yAxis: {
      min: -8,
      max: 8,
      axisLine: { show: false },
      axisTick: { show: false },
      axisLabel: { show: true, formatter: '{value} 米' },
      splitLine: { show: true }
    },
    series: [
      {
        type: 'scatter',
        data: iphoneList.value,
        symbolSize: function (data) {
          return (data[3] * 60) || 0.1; // 根据距离动态调整圆圈大小
        },
        itemStyle: {
          color: '#f9f0ff',
          borderColor: '#722ed1',
          borderWidth: 1
        },
        tooltip: {
          formatter: (params) => {
            const [x, y, ip,r] = params.data;
            return `预估
                    <br>设备: ${ip}在圆圈内
                    <br>坐标: (${x}米, ${y}米)
                    <br>半径: ${r || 0.1} 米`;
          }
        }
      },
      {
        type: 'scatter',
        data: apList.value,
        symbolSize: 20,
        itemStyle: {
          color: 'transparent',
            borderColor: '#000',
            opacity: 0.3,
          borderWidth: 2
        },
        tooltip: {
          show: true,
          formatter: (params) =>
            `AP: ${params.data[2]}<br>坐标: (${params.data[0]} 米, ${params.data[1]} 米)`
        }
      },
      // 区域标记保持不变
      {
        type: 'scatter',
        markArea: {
          silent: true,
          itemStyle: { opacity: 0.3 },

          data: [
            [
              {
                xAxis: 0, yAxis: 0, itemStyle: { color: 'rgba(255, 0, 0, 0.2)' },
                label: { show: true, formatter: '会议室', position: 'insideTopRight', fontSize: 16, fontWeight: 'bold', color: '#722ed1', }
              },
              { xAxis: 8, yAxis: 8 }
            ],
            [
              {
                xAxis: -8, yAxis: 0, itemStyle: { color: 'rgba(0,255,0,0.3)' },
                label: { show: true, formatter: '办公室A', position: 'insideTopLeft', fontSize: 16, fontWeight: 'bold', color: '#722ed1', }
              },
              { xAxis: 0, yAxis: 8 }
            ],
            [
              {
                xAxis: -8, yAxis: -8, itemStyle: { color: 'rgba(0,0,255,0.3)' },
                label: { show: true, formatter: '办公室B', position: 'insideBottomLeft', fontSize: 16, fontWeight: 'bold', color: '#722ed1', }
              },
              { xAxis: 0, yAxis: 0 }
            ],
            [
              {
                xAxis: 0, yAxis: -8, itemStyle: { color: 'rgba(255,255,0,0.3)' },
                label: { show: true, formatter: '仓库', position: 'insideBottomRight', fontSize: 16, fontWeight: 'bold', color: '#722ed1', }
              },
              { xAxis: 8, yAxis: 0 }
            ]
          ]
        }
      }
    ]
  };
}

const deviceIPs = ["*************", "*************"]; // 多个设备 IP
const socketMap = new Map(); // 用来存放多个 socket 引用
function initWebSocket() {
  iphoneList.value = []; // 每次清空列表，重新定位

  deviceIPs.forEach((ip) => {
    const ws = new WebSocket("ws://" + ip + ":5008");
    socketMap.set(ip, ws);

    const timeoutId = setTimeout(() => {
      if (socketMap.get(ip)) {
        console.warn(`设备 ${ip} 超时关闭 WebSocket`);
        socketMap.get(ip).close();
        socketMap.delete(ip);
      }
    }, 20000);

    ws.onopen = () => {
      sendMessage([{ type: "get", value: "location" }], ws);
      ElNotification({
        title: '发送指令',
        message: `设备 ${ip} 定位指令已发送`,
        type: 'success',
        position: 'top-left',
      });
    };

    ws.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        const wifiList = data.wifi;
        const handleList = handleIphoneLocation(wifiList, apList.value, ip);
        if (handleList.length > 0) {
          iphoneList.value.push(handleList[0]);
          console.log("接收到设备", ip, "定位数据:", handleList[0]);
          option.value.series[0].data = iphoneList.value;
          myChart.value.setOption(option.value, true);
        }
      } catch (err) {
        console.error(`设备 ${ip} 消息解析失败`, err);
      } finally {
        clearTimeout(timeoutId);
        if (socketMap.get(ip)) {
          socketMap.get(ip).close();
          socketMap.delete(ip);
        }
      }
    };

    ws.onerror = (err) => {
      console.error(`设备 ${ip} WebSocket 错误:`, err);
    };

    ws.onclose = () => {
      console.warn(`设备 ${ip} WebSocket 已关闭`);
    };
  });
}


// 👉 发送消息
function sendMessage(data, ws) {
  if (ws && ws.readyState === WebSocket.OPEN) {
    ws.send(JSON.stringify(data));
  } else {
    console.warn("WebSocket 未连接");
  }
}
// 👉 页面卸载时关闭连接
onUnmounted(() => {
  for (const [ip, ws] of socketMap) {
    ws.close();
  }
  socketMap.clear();
});


</script>
<style lang="scss" scoped>
.electronicFence {
  padding: 10px;
  background-color: #f5f5f5;
  height: calc(100vh - 118px);
}
</style>
