<template>
    <div class="lf-content file">
        <el-row>
            <el-col :span="1.5">
                <el-button type="success" text bg @click="handleImport">上传文件</el-button>
            </el-col>
        </el-row>
        <div class="file-content">
            <div class="path">
                <span>
                    <svg aria-hidden="true" class="svg-icon" style="font-size: 20px;padding-top: 4px;" @click="upOne">
                        <use :xlink:href="`#icon-xiangshang`"></use>
                    </svg>
                </span>
                <span @click="toTop">根目录</span>
                <span v-for="item, index in contentList" @click="toContent(item, index)">/{{ item.label }}</span>
            </div>
            <div class="file-list">
                <div class="item addItem">
                    <svg aria-hidden="true" class="svg-icon" style="font-size: 48px; padding-bottom: 4px;"
                        @click="createFolder">
                        <use :xlink:href="'#icon-addFile'"></use>
                    </svg>
                </div>
                <el-empty description="空空以空空" v-if="currentList.length === 0" />
                <div class="item" v-for="item, index in currentList" v-else>
                    <!-- 文件夹 -->
                    <template v-if="typeof item === 'object'">
                        <svg aria-hidden="true" class="svg-icon" style="font-size: 48px; padding-bottom: 4px"
                            @contextmenu.prevent="handleRightClick(Object.keys(item)[0])"
                            @dblclick="currentClick(Object.keys(item)[0], index)">
                            <use :xlink:href="'#icon-file'"></use>
                        </svg>
                        <el-tooltip class="box-item" effect="light" :content="Object.keys(item)[0]"><el-text
                                class="mx-1" truncated>{{ Object.keys(item)[0] }}</el-text></el-tooltip>
                    </template>
                    <!-- 文件 -->
                    <template v-else>
                        <svg aria-hidden="true" class="svg-icon" style="font-size: 48px; padding-bottom: 4px"
                            @contextmenu.prevent="handleRightClick(item, 'file')">
                            <use :xlink:href="`#icon-${item.split('.').pop()}`"></use>
                        </svg>
                        <el-tooltip class="box-item" effect="light" :content="item"><el-text class="mx-1" truncated>{{
                            item }}</el-text></el-tooltip>
                    </template>
                </div>
            </div>
        </div>
        <!-- 用户导入对话框 -->
        <el-dialog :title="upload.title" v-model="upload.open" width="400px" append-to-body>
            <el-upload ref="uploadRef" :limit="1" accept=".apk,.doc,.zip,.jpg,.pdf,.png,.xls,.zip,.txt,.xml"
                :headers="upload.headers" :action="upload.url" :disabled="upload.isUploading" :data="setFilePath"
                :on-progress="handleFileUploadProgress" :on-success="handleFileSuccess" :auto-upload="false" drag>
                <el-icon class="el-icon--upload"><upload-filled /></el-icon>
                <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em>
                    <div class="el-upload__tip">只能上传apk/doc/zip/jpg/pdf/png/xls/zip/txt/xml文件，且不超过100MB</div>
                </div>
            </el-upload>
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" @click="submitFileForm">确 定</el-button>
                    <el-button @click="upload.open = false">取 消</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>
<script setup>
import { ElMessageBox, ElMessage } from "element-plus";
import { getToken } from "@/utils/auth";
import { getFolderList, getApkList, addFolder, deleteItem } from "@/api/apk.js";
const { proxy } = getCurrentInstance();

const dataList = ref([]); // 所有数据
const currentList = ref([]); // 当前数据
const contentList = ref([]); // 当前路径
const getList = () => {
    getFolderList().then(res => {
        console.log(res.data[0].filePath);
        dataList.value = res.data[0].filePath
        // currentList.value = res.data[0].filePath
        handleFileCurrentList()
    });
};
// 删除按钮
function handleRightClick(item, type) {
    console.log(item);
    ElMessageBox.confirm(
        `确定要删除 ${item} 吗？`,
        "提示",
        {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
        }
    )
        .then(() => {
            let str;
            let contentPath;
            let path;
            contentList.value.forEach((item, index) => {
                if (index == 0) {
                    contentPath = item.label;
                } else {
                    contentPath += "/" + item.label;
                }
            });
            contentList.value.length == 0 ? str = "/" : str = "/" + contentPath + "/";
            if (type) { // 文件
                path = '/filePath' + str;
                deleteItem({ path, apkName: item }).then(res => {
                    // 这里执行删除操作
                    ElMessage.success("删除成功");
                    getList();
                }, e => {
                    ElMessage.error(e.message)
                })
            } else { // 文件夹
                path = '/filePath' + str + item;
                deleteItem({ path }).then(res => {
                    // 这里执行删除操作
                    ElMessage.success("删除成功");
                    getList();
                }, e => {
                    ElMessage.error(e.message)
                })
            }
        })
        .catch(() => {
            ElMessage.info("取消删除");
        });
}
// 进入文件夹
function currentClick(item, index) {
    contentList.value.push({ label: item, index: index });
    currentList.value = currentList.value[index]["" + item + ""];
}
// 返回上一级
function upOne() {
    if (contentList.value.length > 0) {
        contentList.value.pop();
        if (contentList.value.length == 0) {
            currentList.value = dataList.value
            return
        }
        handleFileCurrentList()
    } else {
        ElMessage.warning("已经到达根目录");
    }
}
// 返回根目录
function toTop() {
    contentList.value = [];
    currentList.value = dataList.value;
}
// 处理当前点击的文件夹
function toContent(item, index) {
    contentList.value.splice(index + 1);
    handleFileCurrentList();
}
// 处理当前要显示的文件数据
function handleFileCurrentList() {
    if(contentList.value.length == 0) {
        currentList.value = dataList.value;
        return
    }
    currentList.value = contentList.value.reduce((prev, cur) => {
        prev = prev[cur.index]["" + cur.label + ""];
        return prev;
    }, dataList.value);
}

// 新建文件夹
function createFolder() {
    ElMessageBox.prompt("请输入文件夹名称", "新建文件夹", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        inputPattern: /^[a-zA-Z0-9_]+$/,
        inputErrorMessage: "文件夹名称只能包含字母、数字和下划线",
    })
        .then(({ value }) => {
            let str;
            let contentPath;
            contentList.value.forEach((item, index) => {
                if (index == 0) {
                    contentPath = item.label;
                } else {
                    contentPath += "/" + item.label;
                }
            });
            contentList.value.length == 0 ? str = "/" : str = "/" + contentPath + "/";
            let path = '/filePath' + str + value;
            console.log(path);
            addFolder(path).then(res => {
                console.log(res);
                // 这里执行新建操作
                ElMessage.success("新建成功");
                getList();
            });

        })
        .catch(() => {
            ElMessage.info("取消新建");
        });
}


/*** 用户上传参数 */
const upload = reactive({
    // 是否显示弹出层（用户导入）
    open: false,
    // 弹出层标题（用户导入）
    title: "",
    // 是否禁用上传
    isUploading: false,
    // 是否更新已经存在的用户数据
    updateSupport: 0,
    // 设置上传的请求头部
    headers: { Authorization: "Bearer " + getToken() },
    // 上传的地址
    url: import.meta.env.VITE_APP_BASE_API + "/android/file/pushApk"
});
/** 导入按钮操作 */
function handleImport() {
    upload.title = "导入APK";
    upload.open = true;
};

/** 设置上传的文件路径 */
function setFilePath() {
    let str;
    let contentPath;
    contentList.value.forEach((item, index) => {
        if (index == 0) {
            contentPath = item.label;
        } else {
            contentPath += "/" + item.label;
        }
    });
    contentList.value.length == 0 ? str = "/" : str = "/" + contentPath;
    return { uploadPath: '/filePath' + str };
};
/**文件上传中处理 */
const handleFileUploadProgress = (event, file, fileList) => {
    upload.isUploading = true;
};

/** 文件上传成功处理 */
const handleFileSuccess = (response, file, fileList) => {
    upload.open = false;
    upload.isUploading = false;
    proxy.$refs["uploadRef"].handleRemove(file);
    proxy.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + "</div>", "上传结果", { dangerouslyUseHTMLString: true });
    getList();
};
/** 提交上传文件 */
function submitFileForm() {
    proxy.$refs["uploadRef"].submit();
};
getList();
</script>
<style lang="scss" scoped>
.file {
    .file-content {
        width: 100%;
        height: calc(100vh - 170px);
        border: 1px solid #ccc;
        margin-top: 10px;
        padding: 5px;
        background-color: #f5f5f5;
        overflow: auto;

        .path {
            width: 100%;
            height: 30px;
            background-color: #f5f5f5;
            border-radius: 10px;
            overflow-y: auto;
            line-height: 30px;
            padding: 0 5px;
            font-family: '宋体';

            span {
                line-height: 30px;
                padding: 2px;

                -webkit-user-select: none;
                /* Safari */
                -moz-user-select: none;
                /* Firefox */
                -ms-user-select: none;
                /* Internet Explorer/Edge */
                user-select: none;
                /* Standard syntax */
            }

            span:hover {
                background-color: #aaa;
                border-radius: 4px;
                box-sizing: border-box;
                cursor: pointer;
            }
        }

        .item {
            float: left;
            margin-right: 10px;
            margin-bottom: 10px;
            width: 80px;
            height: 100px;
            border-radius: 10px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            transition: all 0.2s linear;
            border: 1px dashed #ccc;
            cursor: pointer;

            -webkit-user-select: none;
            /* Safari */
            -moz-user-select: none;
            /* Firefox */
            -ms-user-select: none;
            /* Internet Explorer/Edge */
            user-select: none;
            /* Standard syntax */

        }

        .item:hover {
            box-shadow: 0 2px 18px 0 rgba(0, 0, 0, 0.08);
            border-radius: 8px;
            border: 1px dashed #aaa;
        }

        .addItem {
            color: #1afa2922;
            transition: all 0.2s linear;

            &:hover {
                color: #1afa29;
            }
        }

        .type-list {
            width: 100%;
            height: 100%;
            background-color: aqua;
        }
    }
}

.demo-tabs .custom-tabs-label .el-icon {
    vertical-align: middle;
}

.demo-tabs .custom-tabs-label span {
    vertical-align: middle;
    margin-left: 4px;
}

.el-tabs--border-card {
    height: 100%;
    overflow: hidden;
}

:deep(.el-tabs__content) {
    width: 100%;
    height: calc(100% - 40px);
    overflow: hidden;
}

// 输入框的margin-bottom
:deep(.el-form-item) {
    margin-bottom: 8px !important;
}
</style>