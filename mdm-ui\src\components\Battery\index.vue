<template>
    <div class="Battery">
      <div class="content" :style="{ border: borderColor }">
        <div class="level" :style="levelStyle"></div>
      </div>
      <div class="head" :style="{ border: borderColor }"></div>
      <span>{{ isEmpty ? '' : value + '%' }}</span>
      <div class="slash" v-if="isEmpty"></div>
    </div>
  </template>
  
  <script setup>
  import { defineProps, computed } from 'vue'
  
  const props = defineProps({
    value: {
      type: Number,
      default: null,
    },
  })
  
  // 判断是否为空
  const isEmpty = computed(() =>
    props.value === null || props.value === undefined || props.value === ''
  )
  
  // 边框颜色
  const borderColor = computed(() =>
    isEmpty.value ? '2px solid #999' : '2px solid #000'
  )
  
  // 电量颜色
  const batteryColor = computed(() => {
    if (isEmpty.value) return '#999'
    if (props.value > 75) return '#4caf50'
    if (props.value > 30) return '#ff9800'
    return '#f44336'
  })
  
  // 电量样式
  const levelStyle = computed(() => ({
    width: isEmpty.value ? '0%' : Math.min(props.value, 100) + '%',
    backgroundColor: batteryColor.value,
  }))
  </script>
  
  <style scoped lang="scss">
  .Battery {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
    font-size: 12px;
    font-weight: 700;
  
    .content {
      position: relative;
      width: 28px;
      height: 14px;
      border-radius: 3px;
      overflow: hidden;
      box-sizing: border-box;
    }
  
    .level {
      height: 100%;
      transition: width 0.3s ease;
    }
  
    .head {
      width: 4px;
      height: 6px;
      background-color: #000;
      margin-left: -3px;
      border-radius: 2px;
    }
    .slash {
      width: 1px;
      height: 20px;
      background-color: #999;
      position: absolute;
      top: 0;bottom: 0;left: 0;right: 0;
      margin: auto;
      // transform-origin: center; // 设置旋转点为中心
      transform: rotate(-17deg) translateX(-4px);
    }
  }
  </style>
  