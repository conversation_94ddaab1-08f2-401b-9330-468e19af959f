<template>
    <div class="app-container">
        <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
            <el-form-item label="激活码" prop="code">
                <el-input v-model="queryParams.code" placeholder="请输入激活码" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="激活时长" prop="duration">
                <el-input v-model="queryParams.duration" placeholder="请输入激活时长" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item>
                <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
                <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain icon="Plus" @click="handleAdd"
                    v-hasPermi="['pc:activityCode:add']">新增</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate"
                    v-hasPermi="['pc:activityCode:edit']">修改</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete"
                    v-hasPermi="['pc:activityCode:remove']">删除</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="warning" plain icon="Download" @click="handleExport"
                    v-hasPermi="['pc:activityCode:export']">导出</el-button>
            </el-col>
            <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>

        <el-table v-loading="loading" :data="activityCodeList" @selection-change="handleSelectionChange" size="small" border class="lf-table-content-height">
            <el-table-column type="selection" width="55" align="center" />
            <el-table-column label="序号" type="index" width="55" align="center" />
            <el-table-column label="激活码" align="center" prop="code" width="400" show-overflow-tooltip />
            <el-table-column label="设备数量" align="center" prop="deviceSum" width="100" show-overflow-tooltip />
            <el-table-column label="过期时间" align="center">
                <template #default="scope">
                    {{
                        scope.row.expireTime
                            ? formatDate(scope.row.expireTime - 0)
                    : '永久'
                    }}
                </template>
            </el-table-column>

            <el-table-column label="状态" align="center">
                <template #default="scope">
                    <el-tag v-if="scope.row.type === 0" type="success">正常</el-tag>
                    <el-tag v-else-if="scope.row.type === 1" type="danger">过期</el-tag>
                    <el-tag v-else type="info">未知</el-tag>
                </template>
            </el-table-column>
            <!-- <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                <template #default="scope">
                    <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)"
                        v-hasPermi="['pc:activityCode:edit']">修改</el-button>
                    <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)"
                        v-hasPermi="['pc:activityCode:remove']">删除</el-button>
                </template>
            </el-table-column> -->
        </el-table>

        <pagination v-show="total >= 0" :total="total" v-model:page="queryParams.pageNum"
            v-model:limit="queryParams.pageSize" @pagination="getList" />

        <!-- 添加或修改注册码对话框 -->
        <el-dialog :title="title" v-model="open" width="500px" append-to-body>
            <el-form ref="activityCodeRef" :model="form" :rules="rules" label-width="80px">
                <el-form-item label="激活码" prop="code">
                    <el-input v-model="form.code" placeholder="请输入激活码" />
                </el-form-item>
                <!-- <el-form-item label="激活时长" prop="duration">
            <el-input v-model="form.duration" placeholder="请输入激活时长" />
          </el-form-item> -->
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" @click="submitForm">确 定</el-button>
                    <el-button @click="cancel">取 消</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script setup name="ActivityCode">
import { listActivityCode, getActivityCode, delActivityCode, addActivityCode, updateActivityCode } from "@/api/pc/activityCode.js";

const { proxy } = getCurrentInstance();

const activityCodeList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

const data = reactive({
    form: {},
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        code: null,
        duration: null,
        type: null,
    },
    rules: {
        code: [
            { required: true, message: "激活码不能为空", trigger: "blur" },
            // { min: 36, max: 36, message: "激活码长度必须为36位", trigger: "blur" }
        ]
    }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询注册码列表 */
function getList() {
    loading.value = true;
    listActivityCode(queryParams.value).then(response => {
        console.log(response.rows);
        activityCodeList.value = response.rows;
        total.value = response.total;
        loading.value = false;
    });
}

// 取消按钮
function cancel() {
    open.value = false;
    reset();
}

// 表单重置
function reset() {
    form.value = {
        id: null,
        code: null,
        duration: null,
        type: null,
        createTime: null
    };
    proxy.resetForm("activityCodeRef");
}

/** 搜索按钮操作 */
function handleQuery() {
    queryParams.value.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
    ids.value = selection.map(item => item.id);
    single.value = selection.length != 1;
    multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
    reset();
    open.value = true;
    title.value = "添加注册码";
}

/** 修改按钮操作 */
function handleUpdate(row) {
    reset();
    const _id = row.id || ids.value
    getActivityCode(_id).then(response => {
        form.value = response.data;
        open.value = true;
        title.value = "修改注册码";
    });
}

/** 提交按钮 */
function submitForm() {
    proxy.$refs["activityCodeRef"].validate(valid => {
        if (valid) {
            if (form.value.id != null) {
                updateActivityCode(form.value).then(response => {
                    proxy.$modal.msgSuccess("修改成功");
                    open.value = false;
                    getList();
                });
            } else {
                addActivityCode(form.value).then(response => {
                    proxy.$modal.msgSuccess("新增成功");
                    open.value = false;
                    getList();
                });
            }
        }
    });
}

/** 删除按钮操作 */
function handleDelete(row) {
    const _ids = row.id || ids.value;
    proxy.$modal.confirm('是否确认删除注册码编号为"' + _ids + '"的数据项？').then(function () {
        return delActivityCode(_ids);
    }).then(() => {
        getList();
        proxy.$modal.msgSuccess("删除成功");
    }).catch(() => { });
}

/** 导出按钮操作 */
function handleExport() {
    proxy.download('pc/activityCode/export', {
        ...queryParams.value
    }, `activityCode_${new Date().getTime()}.xlsx`)
}

function formatDate(timestamp) {
  const date = new Date(timestamp);
  const Y = date.getFullYear();
  const M = String(date.getMonth() + 1).padStart(2, '0');
  const D = String(date.getDate()).padStart(2, '0');
  const H = String(date.getHours()).padStart(2, '0');
  const m = String(date.getMinutes()).padStart(2, '0');
  const s = String(date.getSeconds()).padStart(2, '0');
  return `${Y}-${M}-${D} ${H}:${m}:${s}`;
}

getList();
</script>
<style lang="scss" scoped>
.lf-table-content-height {
    height: calc(100vh - 250px) !important;
}
</style>