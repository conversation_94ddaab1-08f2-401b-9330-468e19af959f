export function handleTree(data) {
    let tree = [];
    if (Array.isArray(data)) {
        for (let item of data) {
            tree.push(...handleTree(item));
        }
    } else if (typeof data === 'object' && data !== null) {
        for (let key in data) {
            if (Array.isArray(data[key])) {
                tree.push({
                    label: key,
                    value: key,
                    children: handleTree(data[key])
                });
            }
        }
    }
    return tree;
}