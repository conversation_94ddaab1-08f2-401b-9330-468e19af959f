import request from '@/utils/request'

// 查询组与apk绑定列表
export function listGroupApk(query) {
    
  return request({
    url: '/pc/groupApk/list',
    method: 'get',
    params: query
  })
}

// 查询组与apk绑定详细
export function getGroupApk(id) {
  return request({
    url: '/pc/groupApk/' + id,
    method: 'get'
  })
}

// 新增组与apk绑定
export function addGroupApk(data) {
  return request({
    url: '/pc/groupApk',
    method: 'post',
    data: data
  })
}

// 修改组与apk绑定
export function updateGroupApk(data) {
  return request({
    url: '/pc/groupApk',
    method: 'put',
    data: data
  })
}

// 删除组与apk绑定
export function delGroupApk(id) {
  return request({
    url: '/pc/groupApk/' + id,
    method: 'delete'
  })
}
