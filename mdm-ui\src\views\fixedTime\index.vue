<template>
    <div class="lf-content">
        <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="124px">
            <!-- <el-form-item label="推送时间" prop="pushTime">
                <el-date-picker clearable v-model="queryParams.pushTime" type="date" value-format="YYYY-MM-DD"
                    placeholder="请选择推送时间" @keyup.enter="handleQuery">
                </el-date-picker>
            </el-form-item> -->
            <el-form-item label="是否覆盖原APP" prop="checkedPackage">
                <el-select v-model="queryParams.checkedPackage" placeholder="是否覆盖原APP" clearable @change="handleQuery"
                    style="width: 200px;">
                    <el-option label="不覆盖" value="0" />
                    <el-option label="覆盖" value="1" />
                </el-select>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
                <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain icon="Plus" @click="handleAdd"
                    v-hasPermi="['job:job:add']">新增</el-button>
            </el-col>
            <!-- <el-col :span="1.5">
                <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate"
                    v-hasPermi="['job:job:edit']">修改</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete"
                    v-hasPermi="['job:job:remove']">删除</el-button>
            </el-col> -->
            <el-col :span="1.5">
                <el-button type="warning" plain icon="Download" @click="handleExport"
                    v-hasPermi="['job:job:export']">导出</el-button>
            </el-col>
            <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>

        <el-table v-loading="loading" :data="jobList" @selection-change="handleSelectionChange" :size="size" border
            class="lf-table-content-height">
            <el-table-column type="selection" width="55" align="center" />
            <el-table-column type="index" label="序号" width="50" align="center" show-overflow-tooltip />
            <el-table-column label="推送时间" align="center" prop="pushTime" width="150" show-overflow-tooltip sortable>
                <template #default="scope">
                    <span>{{ parseTime(scope.row.pushTime, '{y}-{m}-{d} {h}:{i}') }}</span>
                </template>
            </el-table-column>
            <el-table-column label="所选设备" align="center" width="120" show-overflow-tooltip>
                <template #default="scope">
                    <el-button link type="primary" @click="showDeviceDetails(scope.row)">
                        查看设备 ({{ scope.row.deviceIds ? scope.row.deviceIds.split(',').length : 0 }})
                    </el-button>
                </template>
            </el-table-column>
            <el-table-column label="推送APK" align="center" width="120" show-overflow-tooltip>
                <template #default="scope">
                    <el-button link type="primary" @click="showApkDetails(scope.row)">
                        查看APK ({{ scope.row.apkIds ? scope.row.apkIds.split(',').length : 0 }})
                    </el-button>
                </template>
            </el-table-column>
            <el-table-column label="应安装APK总数" width="120" align="center">
                <template #default="scope">
                    {{ (scope.row.deviceIds ? scope.row.deviceIds.split(',').length : 0) * (scope.row.apkIds ?
                        scope.row.apkIds.split(',').length : 0) }}
                </template>
            </el-table-column>
            <el-table-column label="已安装APK总数" width="120" align="center" prop="installSum">

            </el-table-column>
            <el-table-column label="是否覆盖原APP" align="center" prop="checkedPackage" width="120" show-overflow-tooltip>
                <template #default="scope">
                    <span>{{ scope.row.checkedPackage == 1 ? '覆盖' : (scope.row.checkedPackage == 0 ? '不覆盖' : '未知')
                    }}</span>
                </template>
            </el-table-column>
            <el-table-column label="是否启用" align="center" width="100">
                <template #default="scope">
                    <el-switch v-model="scope.row.isEnable" :active-value="1" :inactive-value="0"
                        :disabled="isPushTimeExpired(scope.row.pushTime)" @change="handleStatusChange(scope.row)">
                    </el-switch>
                </template>
            </el-table-column>
            <el-table-column label="推送状态" align="center" prop="status" width="100" show-overflow-tooltip>
                <template #default="scope">
                    <span>{{ scope.row.status == 0 ? '待执行' : (scope.row.status == 1 ? '已执行' : '执行失败') }}</span>
                </template>
            </el-table-column>
            <el-table-column label="创建人" width="100" align="center" prop="nickName" show-overflow-tooltip />
            <el-table-column label="创建时间" align="center" prop="createTime" width="150" show-overflow-tooltip sortable>
                <template #default="scope">
                    <span>{{ parseTime(scope.row.pushTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
                </template>
            </el-table-column>
            <el-table-column label="备注" align="center" prop="remark" show-overflow-tooltip />

            <el-table-column label="操作" align="center" class-name="small-padding fixed-width" fixed="right" width="210"
                show-overflow-tooltip>
                <template #default="scope">
                    <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)"
                        v-hasPermi="['job:job:edit']" :disabled="scope.row.status == 1">修改</el-button>
                    <el-button link type="success" icon="CopyDocument" @click="handleCopy(scope.row)"
                        v-hasPermi="['job:job:add']">复制</el-button>
                    <el-button link type="danger" icon="Delete" @click="handleDelete(scope.row)"
                        v-hasPermi="['job:job:remove']">删除</el-button>
                </template>
            </el-table-column>
        </el-table>
        <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
            v-model:limit="queryParams.pageSize" @pagination="getList" />

        <!-- 添加或修改定时推送抽屉 -->
        <el-drawer v-model="open" :title="title" direction="rtl" size="100%" :before-close="cancel">
            <div class="drawer-content">
                <el-form ref="jobRef" :model="form" :rules="rules" label-width="0px" class="drawer-form">
                    <!-- 时间选择区域 -->
                    <div class="form-section">
                        <div class="section-title">定时任务推送时间</div>
                        <el-form-item prop="pushTime">
                            <div class="time-selector">
                                <el-date-picker v-model="selectedDate" type="date" value-format="YYYY-MM-DD"
                                    format="YYYY-MM-DD" placeholder="请选择日期" :disabled-date="disabledDate"
                                    style="width: 180px;">
                                </el-date-picker>
                                <el-select v-model="selectedTime" placeholder="请选择时间" style="width: 120px;"
                                    @change="updatePushTime">
                                    <el-option v-for="time in timeOptions" :key="time.value" :label="time.label"
                                        :value="time.value" />
                                </el-select>
                            </div>
                        </el-form-item>
                    </div>
                    <!-- 设备组选择区域 -->
                    <div class="form-section">
                        <div class="section-title">选择设备组</div>
                        <el-select v-model="selectedDeviceGroupId" placeholder="请选择设备组"
                            @change="handleDeviceGroupChange" style="width: 200px;">
                            <el-option v-for="group in groupList" :key="group.id" :label="group.groupName"
                                :value="group.id" />
                        </el-select>
                        <div class="selected-info" v-if="selectedDeviceGroupName">
                            已选择组：{{ selectedDeviceGroupName }}
                        </div>
                    </div>
                    <!-- 设备和APK选择区域 -->
                    <div class="selection-main">
                        <!-- 左侧：设备选择 -->
                        <div class="selection-panel">
                            <div class="panel-header">
                                <div class="panel-title">选择推送设备</div>
                                <div class="panel-count">({{ selectedDevices.length }}/{{ deviceList.length }})</div>
                                <div class="panel-actions" v-if="deviceList.length > 0">
                                    <el-checkbox v-model="deviceSelectAll" :indeterminate="deviceIndeterminate"
                                        @change="handleDeviceSelectAll">
                                        全选
                                    </el-checkbox>
                                </div>
                            </div>
                            <div class="panel-content">
                                <div class="device-list" v-loading="deviceLoading">
                                    <el-checkbox-group v-model="selectedDevices" @change="handleDeviceSelectionChange">
                                        <div v-for="device in deviceList" :key="device.id" class="device-item">
                                            <el-checkbox :label="device.id" :disabled="device.status == 2">
                                                <div class="device-info">
                                                    <div class="device-name">{{ device.deviceName || device.sn }}</div>
                                                    <div class="device-detail">IP: {{ device.ip }} | MAC: {{ device.mac
                                                    }}
                                                    </div>
                                                </div>
                                            </el-checkbox>
                                        </div>
                                    </el-checkbox-group>
                                    <div v-if="deviceList.length === 0 && !deviceLoading" class="empty-tip">
                                        请先选择设备组
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 右侧：APK选择 -->
                        <div class="selection-panel">
                            <div class="panel-header">
                                <div class="panel-title">选择推送APK</div>
                                <div class="panel-count">({{ selectedApks.length }}/{{ apkList.length }})</div>
                                <div class="panel-actions" v-if="apkList.length > 0">
                                    <el-checkbox v-model="apkSelectAll" :indeterminate="apkIndeterminate"
                                        @change="handleApkSelectAll">
                                        全选
                                    </el-checkbox>
                                </div>
                            </div>
                            <div class="panel-content">
                                <div class="group-info" v-if="selectedDeviceGroupName">
                                    <div class="info-item">
                                        <span class="label">当前组：</span>
                                        <span class="value">{{ selectedDeviceGroupName }}</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="label">已选设备：</span>
                                        <span class="value">{{ selectedDevices.length }} 个</span>
                                    </div>
                                </div>
                                <div class="apk-list" v-loading="apkLoading">
                                    <el-checkbox-group v-model="selectedApks" @change="handleApkSelectionChange">
                                        <div v-for="apk in apkList" :key="apk.apkId" class="apk-item">
                                            <el-checkbox :label="apk.apkId">
                                                <div class="apk-info">
                                                    <img :src="`http://${apk.apkImg || apk.apkImage}`" alt="APK图标"
                                                        class="apk-icon" @error="handleImageError" />
                                                    <div class="apk-detail">
                                                        <div class="apk-name">{{ apk.apkName }}</div>
                                                        <div class="apk-package">{{ apk.apkPackage }}</div>
                                                    </div>
                                                </div>
                                            </el-checkbox>
                                        </div>
                                    </el-checkbox-group>
                                    <div v-if="apkList.length === 0 && !apkLoading && selectedDeviceGroupId"
                                        class="empty-tip">
                                        该组暂无绑定的APK策略
                                    </div>
                                    <div v-if="!selectedDeviceGroupId" class="empty-tip">
                                        请先选择设备组
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- 覆盖选项 -->
                    <div class="form-section">
                        <div class="section-title">是否覆盖原APP</div>
                        <el-form-item prop="checkedPackage">
                            <el-select v-model="form.checkedPackage" placeholder="请选择是否覆盖原APP" style="width: 200px;">
                                <el-option label="不覆盖" value="0" />
                                <el-option label="覆盖" value="1" />
                            </el-select>
                        </el-form-item>
                    </div>
                    <!-- 备注 -->
                    <div class="form-section">
                        <div class="section-title">备注</div>
                        <el-input v-model="form.remark" placeholder="请输入备注" :autosize="{ minRows: 2, maxRows: 4 }"
                            type="textarea" />
                    </div>
                </el-form>
                <!-- 操作按钮 -->
                <div class="drawer-footer">
                    <el-button type="primary" @click="submitForm">确 定</el-button>
                    <el-button @click="cancel">取 消</el-button>
                </div>
            </div>
        </el-drawer>

        <!-- 设备详情对话框 -->
        <el-dialog v-model="deviceDetailOpen" title="设备详情" width="1000px" style="height: 600px;" append-to-body>
            <el-table :data="deviceDetailList" size="small" border class="deviceDetail_height">
                <el-table-column type="index" label="序号" width="60" align="center" />
                <el-table-column label="设备型号" prop="model" show-overflow-tooltip />
                <el-table-column label="设备SN" prop="sn" show-overflow-tooltip />
                <el-table-column label="IP地址" prop="ip" show-overflow-tooltip />
                <el-table-column label="MAC地址" prop="mac" show-overflow-tooltip />
                <el-table-column label="所属组" prop="groupName" show-overflow-tooltip />
                <el-table-column label="安装数量" show-overflow-tooltip>
                    <template #default="scope">
                        <el-button link type="primary" @click="showInstalledApks(scope.row)">
                            {{ scope.row.apkList ? scope.row.apkList.length : 0 }}
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
        </el-dialog>

        <!-- APK详情对话框 -->
        <el-dialog v-model="apkDetailOpen" title="APK详情" width="800px" style="height: 600px;" append-to-body>
            <el-table :data="apkDetailList" size="small" border class="apkDetail_height">
                <el-table-column type="index" label="序号" width="60" align="center" />
                <el-table-column label="APK图标" width="80" align="center">
                    <template #default="scope">
                        <div style="display: flex; justify-content: center; align-items: center;">
                            <img :src="`http://${scope.row.apkImg || scope.row.apkImage}`" alt="APK图标"
                            style="width: 24px; height: 24px;" @error="handleImageError" />
                        </div>
                    </template>
                </el-table-column>
                <el-table-column label="APK名称" prop="apkName" show-overflow-tooltip />
                <el-table-column label="包名" prop="apkPackage" show-overflow-tooltip />
                <el-table-column label="版本" prop="version" show-overflow-tooltip />
            </el-table>
        </el-dialog>

        <!-- 已安装APK对话框 -->
        <el-dialog v-model="installedApkOpen" :title="installedApkTitle" width="800px" style="height: 600px;"
            append-to-body>
            <el-table :data="installedApkList" size="small" border class="installedApk_height">
                <el-table-column type="index" label="序号" width="60" align="center" />
                <el-table-column label="APK图标" width="80" align="center">
                    <template #default="scope">
                        <div style="display: flex; justify-content: center; align-items: center;">
                            <img :src="`http://${scope.row.apkImage}`" alt="APK图标"
                            style="width: 20px; height: 20px; " @error="handleImageError" />
                        </div>
                    </template>
                </el-table-column>
                <el-table-column label="APK名称" prop="apkName" width="120" show-overflow-tooltip />
                <el-table-column label="包名" prop="apkPackage" width="120" show-overflow-tooltip />
                <el-table-column label="版本" prop="version" width="120" show-overflow-tooltip />
                <el-table-column label="版本号" prop="versionCode" width="120" show-overflow-tooltip />
                <el-table-column label="安装时间" prop="createTime" width="150" show-overflow-tooltip fixed="right"/>
            </el-table>
        </el-dialog>
    </div>
</template>

<script setup name="Job">
import { listJob, getJob, delJob, addJob, updateJob, getDeviceByJobId, getApkByJobId } from "@/api/job/index.js";
import { listGroup, listDevice } from "@/api/group";
import { getGroupApk } from "@/api/group_apk";
const { proxy } = getCurrentInstance();
const jobList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const data = reactive({
    form: {},
    queryParams: {
        pageNum: 1,
        pageSize: 20,
        pushTime: null,
        deviceIds: null,
        apkIds: null,
        checkedPackage: null,
        status: null,
    },
    rules: {
        pushTime: [
            { required: true, message: "推送时间不能为空", trigger: "blur" },
            {
                validator: (_, value, callback) => {
                    if (!value) {
                        callback();
                        return;
                    }
                    const selectedDateTime = new Date(value);
                    const currentDateTime = new Date();
                    if (selectedDateTime <= currentDateTime) {
                        callback(new Error("推送时间必须在当前时间之后"));
                    } else {
                        callback();
                    }
                },
                trigger: "blur"
            }
        ],
        deviceIds: [
            { required: true, message: "请选择设备", trigger: "change" }
        ],
        apkIds: [
            { required: true, message: "请选择APK", trigger: "change" }
        ],
        checkedPackage: [
            { required: true, message: "请选择是否覆盖原APP", trigger: "change" }
        ]
    }
});
const { queryParams, form, rules } = toRefs(data);
const size = ref("small");
// 新增的响应式数据
const groupList = ref([]);
const deviceList = ref([]);
const apkList = ref([]);
const deviceLoading = ref(false);
const apkLoading = ref(false);
// 设备选择相关
const selectedDeviceGroupId = ref(null);
const selectedDeviceGroupName = ref('');
const selectedDevices = ref([]);
// APK选择相关（与设备组关联）
const selectedApks = ref([]);
// 全选相关状态
const deviceSelectAll = ref(false);
const deviceIndeterminate = ref(false);
const apkSelectAll = ref(false);
const apkIndeterminate = ref(false);
// 时间选择相关
const selectedDate = ref('');
const selectedTime = ref('');
const timeOptions = ref([]);
// 详情对话框相关
const deviceDetailOpen = ref(false);
const apkDetailOpen = ref(false);
const deviceDetailList = ref([]);
const apkDetailList = ref([]);
// 已安装APK对话框相关
const installedApkOpen = ref(false);
const installedApkList = ref([]);
const installedApkTitle = ref('');
// 生成时间选项（每30分钟一个选项）
function generateTimeOptions() {
    const options = [];
    for (let hour = 0; hour < 24; hour++) {
        for (let minute = 0; minute < 60; minute += 30) {
            const timeStr = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;
            options.push({
                label: timeStr,
                value: timeStr
            });
        }
    }
    timeOptions.value = options;
}
// 更新推送时间
function updatePushTime() {
    if (selectedDate.value && selectedTime.value) {
        form.value.pushTime = `${selectedDate.value} ${selectedTime.value}:00`;
        // 验证时间是否在当前时间之后
        validateDateTime();
    }
}

// 验证选择的日期时间是否在当前时间之后
function validateDateTime() {
    let selectedDateTime;

    // 优先使用form中的pushTime，如果没有则使用selectedDate和selectedTime
    if (form.value.pushTime) {
        selectedDateTime = new Date(form.value.pushTime);
    } else if (selectedDate.value && selectedTime.value) {
        selectedDateTime = new Date(`${selectedDate.value} ${selectedTime.value}:00`);
    } else {
        return true; // 如果没有时间数据，跳过验证
    }

    const currentDateTime = new Date();

    if (selectedDateTime <= currentDateTime) {
        proxy.$modal.msgWarning("推送时间必须在当前时间之后");
        return false;
    }
    return true;
}

// 验证设备和APK选择
function validateSelections() {
    const errors = [];

    // 验证推送时间
    if (!form.value.pushTime) {
        errors.push("请选择推送时间");
    }

    // 验证是否覆盖原APP
    if (form.value.checkedPackage === null || form.value.checkedPackage === undefined || form.value.checkedPackage === '') {
        errors.push("请选择是否覆盖原APP");
    }

    // 验证设备选择
    if (!selectedDevices.value || selectedDevices.value.length === 0) {
        errors.push("请至少选择一个设备");
    }

    // 验证APK选择
    if (!selectedApks.value || selectedApks.value.length === 0) {
        errors.push("请至少选择一个APK");
    }

    if (errors.length > 0) {
        proxy.$modal.msgWarning(errors.join("，"));
        return false;
    }

    return true;
}

// 监听日期变化
watch(selectedDate, () => {
    updatePushTime();
});
// 禁用过去的日期
const disabledDate = (time) => {
    return time.getTime() < Date.now() - 8.64e7; // 禁用昨天之前的日期
};
// 加载组列表
async function loadGroupList() {
    try {
        const response = await listGroup();
        groupList.value = response.data || response;
    } catch (error) {
        console.error('加载组列表失败:', error);
        groupList.value = [];
    }
}
// 设备组选择变化
async function handleDeviceGroupChange(groupId) {
    selectedDeviceGroupId.value = groupId;
    const selectedGroup = groupList.value.find(group => group.id === groupId);
    selectedDeviceGroupName.value = selectedGroup ? selectedGroup.groupName : '';
    // 清空之前选择的设备和APK
    selectedDevices.value = [];
    selectedApks.value = [];
    // 重置全选状态
    deviceSelectAll.value = false;
    deviceIndeterminate.value = false;
    apkSelectAll.value = false;
    apkIndeterminate.value = false;
    if (groupId) {
        // 同时加载设备列表和APK列表
        await Promise.all([
            loadDeviceList(groupId),
            loadApkList(groupId)
        ]);
    } else {
        deviceList.value = [];
        apkList.value = [];
    }
}

// 设备选择变化
function handleDeviceSelectionChange(selectedDeviceIds) {
    selectedDevices.value = selectedDeviceIds;
    // 更新表单数据
    form.value.deviceIds = selectedDeviceIds.join(',');
    // 更新全选状态
    updateDeviceSelectAllStatus();
}
// 设备全选处理
function handleDeviceSelectAll(checked) {
    if (checked) {
        selectedDevices.value = deviceList.value.map(device => device.id);
    } else {
        selectedDevices.value = [];
    }
    form.value.deviceIds = selectedDevices.value.join(',');
    updateDeviceSelectAllStatus();
}
// 更新设备全选状态
function updateDeviceSelectAllStatus() {
    const selectedCount = selectedDevices.value.length;
    const totalCount = deviceList.value.length;

    deviceSelectAll.value = selectedCount === totalCount && totalCount > 0;
    deviceIndeterminate.value = selectedCount > 0 && selectedCount < totalCount;
}
// 加载设备列表（只加载当前组，不包含子组）
async function loadDeviceList(groupId) {
    deviceLoading.value = true;
    try {
        // 添加参数明确指定只查询当前组的设备
        const response = await listDevice({
            groupId,
            onlyCurrentGroup: true,  // 只查询当前组
            includeSubgroups: false  // 不包含子组
        });
        deviceList.value = response.rows.filter(device => device.groupId === groupId && device.status != 2) || [];
        // console.log(groupId);
        // console.log(response);
        // console.log('设备列表（仅当前组）', deviceList.value);
    } catch (error) {
        console.error('加载设备列表失败:', error);
        deviceList.value = [];
    } finally {
        deviceLoading.value = false;
    }
}

// 加载APK列表
async function loadApkList(groupId) {
    apkLoading.value = true;
    try {
        const response = await getGroupApk(groupId);
        console.log(response);
        apkList.value = response.data || [];
        console.log('APK列表', apkList.value);
    } catch (error) {
        console.error('加载APK列表失败:', error);
        apkList.value = [];
    } finally {
        apkLoading.value = false;
    }
}

// APK选择变化
function handleApkSelectionChange(selectedApkIds) {
    selectedApks.value = selectedApkIds;
    console.log('所选APK', selectedApkIds);
    // 更新表单数据
    form.value.apkIds = selectedApkIds.join(',');
    // 更新全选状态
    updateApkSelectAllStatus();
}

// APK全选处理
function handleApkSelectAll(checked) {
    if (checked) {
        selectedApks.value = apkList.value.map(apk => apk.apkId);
    } else {
        selectedApks.value = [];
    }
    form.value.apkIds = selectedApks.value.join(',');
    updateApkSelectAllStatus();
}

// 更新APK全选状态
function updateApkSelectAllStatus() {
    const selectedCount = selectedApks.value.length;
    const totalCount = apkList.value.length;
    apkSelectAll.value = selectedCount === totalCount && totalCount > 0;
    apkIndeterminate.value = selectedCount > 0 && selectedCount < totalCount;
}

// 图片加载错误处理
function handleImageError(event) {
    event.target.src = '/src/assets/images/default-app-icon.png';
}
/** 查询定时推送列表 */
function getList() {
    loading.value = true;
    listJob(queryParams.value).then(response => {
        jobList.value = response.rows;
        console.log(response.rows);
        total.value = response.total;
        loading.value = false;
    });
}

// 取消按钮
function cancel() {
    open.value = false;
    reset();
}

// 表单重置
function reset() {
    form.value = {
        id: null,
        pushTime: null,
        deviceIds: null,
        apkIds: null,
        checkedPackage: null,
        status: null,
        isEnable: 1, // 默认启用
        remark: null,
        createBy: null,
        createTime: null
    };
    // 重置选择状态
    selectedDeviceGroupId.value = null;
    selectedDeviceGroupName.value = '';
    selectedDevices.value = [];
    selectedApks.value = [];
    deviceList.value = [];
    apkList.value = [];
    selectedDate.value = '';
    selectedTime.value = '';
    // 重置全选状态
    deviceSelectAll.value = false;
    deviceIndeterminate.value = false;
    apkSelectAll.value = false;
    apkIndeterminate.value = false;
    proxy.resetForm("jobRef");
}

/** 搜索按钮操作 */
function handleQuery() {
    queryParams.value.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
    ids.value = selection.map(item => item.id);
    single.value = selection.length != 1;
    multiple.value = !selection.length;
}

/** 新增按钮操作 */
async function handleAdd() {
    reset();
    await loadGroupList();
    generateTimeOptions();
    open.value = true;
    title.value = "添加定时推送";
}

/** 复制新增操作 */
async function handleCopy(row) {
    reset();
    await loadGroupList();
    generateTimeOptions();

    // 复制基本信息
    form.value = {
        id: null,
        pushTime: null, // 时间需要重新选择
        deviceIds: row.deviceIds,
        apkIds: row.apkIds,
        checkedPackage: row.checkedPackage,
        status: null,
        isEnable: 1, // 复制时默认启用
        remark: row.remark,
        createBy: null,
        createTime: null
    };

    // 如果有设备ID，尝试解析并设置选择状态
    if (row.deviceIds) {
        try {
            // 使用专门的API获取任务关联的设备信息
            const deviceResponse = await getDeviceByJobId(row.id);
            const jobDevices = deviceResponse.data.filter(device => device.status !== 2) || [];

            if (jobDevices.length > 0) {
                selectedDevices.value = jobDevices.map(device => device.id);

                // 获取第一个设备的组信息
                const firstDevice = jobDevices[0];
                if (firstDevice.groupId) {
                    selectedDeviceGroupId.value = firstDevice.groupId;
                    selectedDeviceGroupName.value = firstDevice.groupName || '';
                    await loadDeviceList(firstDevice.groupId);
                    await loadApkList(firstDevice.groupId);
                }
            }
        } catch (error) {
            console.error('获取任务设备信息失败:', error);
            // 降级到原来的方法
            const deviceIdArray = row.deviceIds.split(',').map(id => Number(id));
            selectedDevices.value = deviceIdArray;
            await loadDeviceGroupFromDeviceIds(deviceIdArray);
        }
    }
    // 如果有APK ID，设置选择状态
    if (row.apkIds) {
        try {
            // 使用专门的API获取任务关联的APK信息
            const apkResponse = await getApkByJobId(row.id);
            const jobApks = apkResponse.data || [];

            if (jobApks.length > 0) {
                selectedApks.value = jobApks.map(apk => apk.id);
                // 获取第一个APK的组ID，加载该组的完整APK列表
                const firstApk = jobApks[0];
                if (firstApk.groupId && apkList.value.length === 0) {
                    await loadApkList(firstApk.groupId);
                }
            }
        } catch (error) {
            console.error('获取任务APK信息失败:', error);
            // 降级到原来的方法
            const apkIdArray = row.apkIds.split(',').map(id => Number(id));
            selectedApks.value = apkIdArray;
        }
    }
    // 更新全选状态
    updateDeviceSelectAllStatus();
    updateApkSelectAllStatus();
    open.value = true;
    title.value = "复制定时推送";
}
// 根据设备ID查找设备组
async function loadDeviceGroupFromDeviceIds(deviceIds) {
    try {
        // 获取所有组
        const groupResponse = await listGroup();
        const groups = groupResponse.data || groupResponse;
        // 遍历组，找到包含这些设备的组
        for (const group of groups) {
            const deviceResponse = await listDevice({
                groupId: group.id,
                onlyCurrentGroup: true,  // 只查询当前组
                includeSubgroups: false  // 不包含子组
            });
            const devices = deviceResponse.rows || deviceResponse.data || [];
            // 检查是否有匹配的设备 - 使用字符串比较避免精度问题
            const hasMatchingDevice = devices.some(device =>
                deviceIds.some(id => String(device.id) === String(id))
            );
            if (hasMatchingDevice) {
                selectedDeviceGroupId.value = group.id;
                selectedDeviceGroupName.value = group.groupName;
                deviceList.value = devices;
                // 同时加载该组的APK
                await loadApkList(group.id);
                // 更新全选状态
                updateDeviceSelectAllStatus();
                updateApkSelectAllStatus();
                break;
            }
        }
    } catch (error) {
        console.error('查找设备组失败:', error);
    }
}
/** 判断推送时间是否已过期 */
function isPushTimeExpired(pushTime) {
    if (!pushTime) return false;
    const pushDateTime = new Date(pushTime);
    const currentDateTime = new Date();
    return pushDateTime < currentDateTime;
}

/** 处理启用状态变化 */
async function handleStatusChange(row) {
    try {
        // 检查推送时间是否小于当前时间
        const pushTime = new Date(row.pushTime);
        const currentTime = new Date();

        if (pushTime < currentTime && row.isEnable === 1) {
            // 如果推送时间已过且尝试启用，则禁止操作
            proxy.$modal.msgWarning("推送时间已过，无法启用该任务");
            // 恢复原状态
            row.isEnable = 0;
            return;
        }

        // 调用API更新启用状态
        const updateData = {
            id: row.id,
            isEnable: row.isEnable
        };

        await updateJob(updateData);
        const statusText = row.isEnable === 1 ? '启用' : '禁用';
        proxy.$modal.msgSuccess(`${statusText}成功`);
        // 刷新列表
        getList();
    } catch (error) {
        console.error('更新启用状态失败:', error);
        proxy.$modal.msgError("更新启用状态失败");
        // 恢复原状态
        row.isEnable = row.isEnable === 1 ? 0 : 1;
    }
}
/** 修改按钮操作 */
async function handleUpdate(row) {
    reset();
    await loadGroupList(); // 获取所有组
    generateTimeOptions(); // 生成时间选项

    const _id = row.id || ids.value
    getJob(_id).then(async response => {
        form.value = response.data;
        // 回显时间数据
        if (response.data.pushTime) {
            const pushTime = response.data.pushTime;
            const [datePart, timePart] = pushTime.split(' ');
            selectedDate.value = datePart;
            selectedTime.value = timePart.substring(0, 5); // 去掉秒数，只保留HH:mm
        }

        // 回显设备数据 - 使用专门的API
        if (response.data.deviceIds) {
            try {
                // 使用专门的API获取任务关联的设备信息
                const deviceResponse = await getDeviceByJobId(response.data.id);
                console.log(deviceResponse);
                const jobDevices = deviceResponse.data.filter(device => device.status !== 2) || []; // 直接过滤掉未激活的设备
                // console.log('任务关联的设备:', jobDevices);
                if (jobDevices.length > 0) {
                    // 设置选中的设备ID
                    selectedDevices.value = jobDevices.map(device => device.id);
                    // console.log('设置selectedDevices为:', selectedDevices.value);

                    // 获取第一个设备的组信息（假设所有设备都在同一组）
                    const firstDevice = jobDevices[0];
                    if (firstDevice.groupId) {
                        selectedDeviceGroupId.value = firstDevice.groupId;
                        selectedDeviceGroupName.value = firstDevice.groupName || '';

                        // 加载该组的所有设备
                        await loadDeviceList(firstDevice.groupId);
                        await loadApkList(firstDevice.groupId);
                        // console.log('加载设备组后，设备列表:', deviceList.value);
                    }
                }
            } catch (error) {
                console.error('获取任务设备信息失败:', error);
                // 降级到原来的方法
                const deviceIdArray = response.data.deviceIds.split(',').map(id => Number(id));
                selectedDevices.value = deviceIdArray;
                await loadDeviceGroupFromDeviceIds(deviceIdArray);
            }
        }

        // 回显APK数据 - 使用专门的API
        if (response.data.apkIds) {
            try {
                // 使用专门的API获取任务关联的APK信息
                const apkResponse = await getApkByJobId(response.data.id);
                console.log('获取任务APK信息:', apkResponse);
                const jobApks = apkResponse.data || []; // 所有选择的APK 11111
                // console.log('任务关联的APK:', jobApks);

                if (jobApks.length > 0) {
                    // 设置选中的APK ID
                    selectedApks.value = jobApks.map(apk => apk.id);
                    // console.log('设置selectedApks为:', selectedApks.value);

                    // 获取第一个APK的组ID，加载该组的完整APK列表
                    const firstApk = jobApks[0];
                    if (firstApk.groupId && apkList.value.length === 0) {
                        await loadApkList(firstApk.groupId);
                    }
                }
            } catch (error) {
                console.error('获取任务APK信息失败:', error);
                // 降级到原来的方法
                const apkIdArray = response.data.apkIds.split(',').map(id => Number(id));
                selectedApks.value = apkIdArray;
            }
        }

        // 更新全选状态
        updateDeviceSelectAllStatus();
        updateApkSelectAllStatus();

        open.value = true;
        title.value = "修改定时推送";
    });
}

/** 提交按钮 */
function submitForm() {
    proxy.$refs["jobRef"].validate(valid => {
        if (valid) {
            // 验证设备和APK选择
            if (!validateSelections()) {
                return;
            }

            // 对于新增任务，验证时间必须在当前时间之后
            if (form.value.id == null) {
                if (!validateDateTime()) {
                    console.log('时间验证失败，当前时间:', new Date(), '选择时间:', form.value.pushTime);
                    return;
                }
                console.log('时间验证通过，当前时间:', new Date(), '选择时间:', form.value.pushTime);
            }

            // 确保form中的设备和APK数据是最新的
            form.value.deviceIds = selectedDevices.value.join(',');
            form.value.apkIds = selectedApks.value.join(',');

            if (form.value.id != null) {
                updateJob(form.value).then(() => {
                    proxy.$modal.msgSuccess("修改成功");
                    open.value = false;
                    getList();
                });
            } else {
                addJob(form.value).then(() => {
                    proxy.$modal.msgSuccess("新增成功");
                    open.value = false;
                    getList();
                });
            }
        }
    });
}

/** 删除按钮操作 */
function handleDelete(row) {
    const _ids = row.id || ids.value;
    proxy.$modal.confirm('是否确认删除定时推送编号为"' + _ids + '"的数据项？').then(function () {
        return delJob(_ids);
    }).then(() => {
        getList();
        proxy.$modal.msgSuccess("删除成功");
    }).catch(() => { });
}

/** 导出按钮操作 */
function handleExport() {
    proxy.download('job/job/export', {
        ...queryParams.value
    }, `job_${new Date().getTime()}.xlsx`)
}

/** 显示设备详情 */
async function showDeviceDetails(row) {
    if (!row.deviceIds) {
        proxy.$modal.msgWarning("该任务没有选择设备");
        return;
    }

    try {
        // 使用专门的API获取任务关联的设备信息
        const deviceResponse = await getDeviceByJobId(row.id);
        // console.log('任务关联的设备:', jobDevices);
        const jobDevices = deviceResponse.data || [];
        console.log('任务关联的设备:', jobDevices);

        if (jobDevices.length > 0) {
            deviceDetailList.value = jobDevices;
            deviceDetailOpen.value = true;
        } else {
            proxy.$modal.msgWarning("未找到该任务的设备信息");
        }
    } catch (error) {
        console.error('获取设备详情失败:', error);
        proxy.$modal.msgError("获取设备详情失败");
    }
}

/** 显示APK详情 */
async function showApkDetails(row) {
    if (!row.apkIds) {
        proxy.$modal.msgWarning("该任务没有选择APK");
        return;
    }

    try {
        // 使用专门的API获取任务关联的APK信息
        const apkResponse = await getApkByJobId(row.id);
        const jobApks = apkResponse.data || [];

        if (jobApks.length > 0) {
            apkDetailList.value = jobApks;
            apkDetailOpen.value = true;
        } else {
            proxy.$modal.msgWarning("未找到该任务的APK信息");
        }
    } catch (error) {
        console.error('获取APK详情失败:', error);
        proxy.$modal.msgError("获取APK详情失败");
    }
}

/** 显示设备已安装APK */
function showInstalledApks(device) {
    if (!device.apkList || device.apkList.length === 0) {
        proxy.$modal.msgWarning("该设备没有安装APK");
        return;
    }

    // 设置对话框标题
    installedApkTitle.value = `${device.sn || device.deviceName} - 已安装APK (${device.apkList.length})`;

    // 设置APK列表数据
    installedApkList.value = device.apkList;

    // 打开对话框
    installedApkOpen.value = true;
}

// function handleStatusChange(row) {
//    let text = row.status === "0" ? "启用" : "停用";
//    proxy.$modal.confirm('确认要"' + text + '""' + row.userName + '"用户吗?').then(function () {
//       return changeUserStatus(row.userId, row.status);
//    }).then(() => {
//       proxy.$modal.msgSuccess(text + "成功");
//    }).catch(function () {
//       row.status = row.status === "0" ? "1" : "0";
//    });
// };

getList();
</script>
<style lang="scss" scoped>
.lf-table-content-height {
    height: calc(100vh - 245px) !important;
}

.deviceDetail_height {
    height: calc(800px - 270px) !important;
}

.apkDetail_height {
    height: calc(800px - 270px) !important;
}

.installedApk_height {
    height: calc(800px - 270px) !important;
}

:deep(.el-table__inner-wrapper:before) {
    height: 0.8px !important;
}

:deep(.el-form-item) {
    margin-bottom: 10px !important;
}

/* Drawer样式 */
.drawer-content {
    height: 100%;
    display: flex;
    width: 100%;
    flex-direction: column;
    align-items: center;

}

.drawer-form {
    flex: 1;
    width: 80%;
    overflow-y: auto;
    padding-right: 10px;
}

.drawer-footer {
    padding: 20px 0 0 0;
    border-top: 1px solid #e4e7ed;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 20px;
    flex-shrink: 0;
}

/* 表单区域样式 */
.form-section {
    margin-bottom: 24px;
    margin-top: 20px;
}

.section-title {
    font-size: 14px;
    font-weight: 600;
    color: #303133;
    margin-bottom: 12px;
    padding-bottom: 8px;
    border-bottom: 1px solid #e4e7ed;
}

.time-selector {
    display: flex;
    gap: 10px;
    align-items: center;
}

/* 设备和APK选择主区域 */
.selection-main {
    display: flex;
    gap: 20px;
    height: 500px;
}

.selection-panel {
    flex: 1;
    display: flex;
    flex-direction: column;
    border: 1px solid #e4e7ed;
    border-radius: 6px;
    overflow: hidden;
}

.panel-header {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    background-color: #f5f7fa;
    border-bottom: 1px solid #e4e7ed;
    gap: 10px;
    flex-shrink: 0;
}

.panel-title {
    font-size: 14px;
    font-weight: 600;
    color: #303133;
}

.panel-count {
    font-size: 12px;
    color: #909399;
}

.panel-actions {
    margin-left: auto;
}

.panel-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    padding: 16px;
}

.selection-container {
    display: flex;
    gap: 20px;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    padding: 15px;
    background-color: #fafafa;
}

.selection-left {
    flex: 1;
    min-width: 200px;
}

.selection-right {
    flex: 2;
    min-width: 300px;
}



.selected-info {
    margin-top: 10px;
    padding: 8px 12px;
    background-color: #f0f9ff;
    border: 1px solid #b3d8ff;
    border-radius: 4px;
    color: #409eff;
    font-size: 12px;
}

.group-info {
    padding: 12px;
    background-color: #f0f9ff;
    border: 1px solid #bfdbfe;
    border-radius: 6px;
    margin-bottom: 10px;
}

.info-item {
    display: flex;
    margin-bottom: 8px;
}

.info-item:last-child {
    margin-bottom: 0;
}

.info-item .label {
    font-weight: 500;
    color: #374151;
    min-width: 80px;
}

.info-item .value {
    color: #1f2937;
    font-weight: 600;
}

.selection-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.selection-header h4 {
    margin: 0;
    color: #303133;
    font-size: 14px;
    font-weight: 600;
}

.selection-actions {
    display: flex;
    align-items: center;
}

.device-list,
.apk-list {
    flex: 1;
    overflow-y: auto;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    padding: 12px;
    background-color: white;
    margin-top: 10px;
}

.device-list::-webkit-scrollbar,
.apk-list::-webkit-scrollbar {
    width: 6px;
}

.device-list::-webkit-scrollbar-track,
.apk-list::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.device-list::-webkit-scrollbar-thumb,
.apk-list::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.device-list::-webkit-scrollbar-thumb:hover,
.apk-list::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

.device-item,
.apk-item {
    margin-bottom: 8px;
    padding: 8px;
    border-radius: 4px;
    transition: background-color 0.2s;
}

.device-item:hover,
.apk-item:hover {
    background-color: #f5f7fa;
}

.device-info {
    margin-left: 8px;
}

.device-name {
    font-weight: 500;
    color: #303133;
    font-size: 13px;
}

.device-detail {
    color: #909399;
    font-size: 11px;
    margin-top: 2px;
}

.apk-info {
    display: flex;
    align-items: center;
    margin-left: 8px;
}

.apk-icon {
    width: 32px;
    height: 32px;
    border-radius: 4px;
    margin-right: 10px;
    object-fit: cover;
}

.apk-detail {
    flex: 1;
}

.apk-name {
    font-weight: 500;
    color: #303133;
    font-size: 13px;
}

.apk-package {
    color: #909399;
    font-size: 11px;
    margin-top: 2px;
}

.empty-tip {
    text-align: center;
    color: #909399;
    font-size: 12px;
    padding: 20px;
}

:deep(.el-checkbox-group) {
    display: block;
}

:deep(.el-checkbox) {
    display: block;
    margin-right: 0;
    margin-bottom: 0;
}

:deep(.el-checkbox__label) {
    width: 100%;
}
</style>
