<template>
    <div class="lf-content">
        <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="124px">
            <el-form-item label="推送时间" prop="pushTime">
                <el-date-picker clearable v-model="queryParams.pushTime" type="date" value-format="YYYY-MM-DD"
                    placeholder="请选择推送时间" @keyup.enter="handleQuery">
                </el-date-picker>
            </el-form-item>
            <el-form-item label="是否覆盖原APP" prop="checkedPackage">
                <el-select v-model="queryParams.checkedPackage" placeholder="是否覆盖原APP" clearable @change="handleQuery"
                    style="width: 200px;">
                    <el-option label="不覆盖" value="0" />
                    <el-option label="覆盖" value="1" />
                </el-select>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
                <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain icon="Plus" @click="handleAdd"
                    v-hasPermi="['job:job:add']">新增</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate"
                    v-hasPermi="['job:job:edit']">修改</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete"
                    v-hasPermi="['job:job:remove']">删除</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="warning" plain icon="Download" @click="handleExport"
                    v-hasPermi="['job:job:export']">导出</el-button>
            </el-col>
            <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>

        <el-table v-loading="loading" :data="jobList" @selection-change="handleSelectionChange" :size="size" border
            class="lf-table-content-height">
            <el-table-column type="selection" width="55" align="center" />
            <el-table-column type="index" label="序号" width="50" align="center" show-overflow-tooltip />
            <el-table-column label="推送时间" align="center" prop="pushTime" width="180" show-overflow-tooltip sortable>
                <template #default="scope">
                    <span>{{ parseTime(scope.row.pushTime, '{y}-{m}-{d} {h}:{i}') }}</span>
                </template>
            </el-table-column>
            <el-table-column label="所选设备" align="center" prop="deviceIds" width="150" show-overflow-tooltip sortable />
            <el-table-column label="推送APK" align="center" prop="apkIds" width="150" show-overflow-tooltip sortable />
            <el-table-column label="是否覆盖原APP" align="center" prop="checkedPackage" width="120" show-overflow-tooltip>
                <template #default="scope">
                    <span>{{ scope.row.checkedPackage === 1 ? '覆盖' : (scope.row.checkedPackage === 0 ? '不覆盖' : '未知')
                    }}</span>
                </template>
            </el-table-column>
            <el-table-column label="推送状态" align="center" prop="status" width="100" show-overflow-tooltip>
                <template #default="scope">
                    <span>{{ scope.row.status === 0 ? '待执行' : (scope.row.status === 1 ? '已执行' : '执行失败') }}</span>
                </template>
            </el-table-column>
            <el-table-column label="备注" align="center" prop="remark" show-overflow-tooltip />
            <el-table-column label="操作" align="center" class-name="small-padding fixed-width" fixed="right"
                show-overflow-tooltip>
                <template #default="scope">
                    <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)"
                        v-hasPermi="['job:job:edit']">修改</el-button>
                    <el-button link type="success" icon="CopyDocument" @click="handleCopy(scope.row)"
                        v-hasPermi="['job:job:add']">复制</el-button>
                    <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)"
                        v-hasPermi="['job:job:remove']">删除</el-button>
                </template>
            </el-table-column>
        </el-table>
        <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
            v-model:limit="queryParams.pageSize" @pagination="getList" />

        <!-- 添加或修改定时推送对话框 -->
        <el-dialog :title="title" v-model="open" width="1000px" append-to-body>
            <el-form ref="jobRef" :model="form" :rules="rules" label-width="120px">
                <el-form-item label="定时任务推送时间" prop="pushTime">
                    <div style="display: flex; gap: 10px; align-items: center;">
                        <el-date-picker v-model="selectedDate" type="date" value-format="YYYY-MM-DD" format="YYYY-MM-DD"
                            placeholder="请选择日期" :disabled-date="disabledDate" style="flex: 1;">
                        </el-date-picker>
                        <el-select v-model="selectedTime" placeholder="请选择时间" style="width: 120px;"
                            @change="updatePushTime">
                            <el-option v-for="time in timeOptions" :key="time.value" :label="time.label"
                                :value="time.value" />
                        </el-select>
                    </div>
                </el-form-item>
                <!-- 设备选择区域 -->
                <el-form-item label="选择推送设备" prop="deviceIds">
                    <div class="selection-container">
                        <div class="selection-left">
                            <h4>选择组</h4>
                            <el-select v-model="selectedDeviceGroupId" placeholder="请选择设备组"
                                @change="handleDeviceGroupChange" style="width: 100%; margin-bottom: 10px;">
                                <el-option v-for="group in groupList" :key="group.id" :label="group.groupName"
                                    :value="group.id" />
                            </el-select>
                            <div class="selected-info" v-if="selectedDeviceGroupName">
                                已选择组：{{ selectedDeviceGroupName }}
                            </div>
                        </div>
                        <div class="selection-right">
                            <div class="selection-header">
                                <h4>选择设备 ({{ selectedDevices.length }}/{{ deviceList.length }})</h4>
                                <div class="selection-actions" v-if="deviceList.length > 0">
                                    <el-checkbox v-model="deviceSelectAll" :indeterminate="deviceIndeterminate"
                                        @change="handleDeviceSelectAll">
                                        全选
                                    </el-checkbox>
                                </div>
                            </div>
                            <div class="device-list" v-loading="deviceLoading">
                                <el-checkbox-group v-model="selectedDevices" @change="handleDeviceSelectionChange">
                                    <div v-for="device in deviceList" :key="device.id" class="device-item">
                                        <el-checkbox :label="device.id">
                                            <div class="device-info">
                                                <div class="device-name">{{ device.deviceName || device.sn }}</div>
                                                <div class="device-detail">IP: {{ device.ip }} | MAC: {{ device.mac }}
                                                </div>
                                            </div>
                                        </el-checkbox>
                                    </div>
                                </el-checkbox-group>
                                <div v-if="deviceList.length === 0 && !deviceLoading" class="empty-tip">
                                    请先选择组
                                </div>
                            </div>
                        </div>
                    </div>
                </el-form-item>
                <!-- APK选择区域 -->
                <el-form-item label="选择推送APK" prop="apkIds">
                    <div class="selection-container">
                        <div class="selection-left">
                            <h4>设备组信息</h4>
                            <div class="group-info" v-if="selectedDeviceGroupName">
                                <div class="info-item">
                                    <span class="label">当前组：</span>
                                    <span class="value">{{ selectedDeviceGroupName }}</span>
                                </div>
                                <div class="info-item">
                                    <span class="label">已选设备：</span>
                                    <span class="value">{{ selectedDevices.length }} 个</span>
                                </div>
                            </div>
                            <div class="empty-tip" v-else>
                                请先选择设备组
                            </div>
                        </div>
                        <div class="selection-right">
                            <div class="selection-header">
                                <h4>该组绑定的APK策略 ({{ selectedApks.length }}/{{ apkList.length }})</h4>
                                <div class="selection-actions" v-if="apkList.length > 0">
                                    <el-checkbox v-model="apkSelectAll" :indeterminate="apkIndeterminate"
                                        @change="handleApkSelectAll">
                                        全选
                                    </el-checkbox>
                                </div>
                            </div>
                            <div class="apk-list" v-loading="apkLoading">
                                <el-checkbox-group v-model="selectedApks" @change="handleApkSelectionChange">
                                    <div v-for="apk in apkList" :key="apk.id" class="apk-item">
                                        <el-checkbox :label="apk.id">
                                            <div class="apk-info">
                                                <img :src="`http://${apk.apkImg || apk.apkImage}`" alt="APK图标"
                                                    class="apk-icon" @error="handleImageError" />
                                                <div class="apk-detail">
                                                    <div class="apk-name">{{ apk.apkName }}</div>
                                                    <div class="apk-package">{{ apk.apkPackage }}</div>
                                                </div>
                                            </div>
                                        </el-checkbox>
                                    </div>
                                </el-checkbox-group>
                                <div v-if="apkList.length === 0 && !apkLoading && selectedDeviceGroupId"
                                    class="empty-tip">
                                    该组暂无绑定的APK策略
                                </div>
                                <div v-if="!selectedDeviceGroupId" class="empty-tip">
                                    请先选择设备组
                                </div>
                            </div>
                        </div>
                    </div>
                </el-form-item>
                <el-form-item label="是否覆盖原APP" prop="checkedPackage">
                    <el-select v-model="form.checkedPackage" placeholder="请选择是否覆盖原APP" style="width: 100%;">
                        <el-option label="不覆盖" value="0" />
                        <el-option label="覆盖" value="1" />
                    </el-select>
                </el-form-item>
                <el-form-item label="备注" prop="remark">
                    <el-input v-model="form.remark" placeholder="请输入备注" />
                </el-form-item>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" @click="submitForm">确 定</el-button>
                    <el-button @click="cancel">取 消</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script setup name="Job">
import { listJob, getJob, delJob, addJob, updateJob } from "@/api/job/index.js";
import { listGroup, listDevice } from "@/api/group";
import { getGroupApk } from "@/api/group_apk";


const { proxy } = getCurrentInstance();

const jobList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

const data = reactive({
    form: {},
    queryParams: {
        pageNum: 1,
        pageSize: 20,
        pushTime: null,
        deviceIds: null,
        apkIds: null,
        checkedPackage: null,
        status: null,
    },
    rules: {
        pushTime: [
            { required: true, message: "推送时间不能为空", trigger: "blur" }
        ],
        deviceIds: [
            { required: true, message: "设备ID不能为空", trigger: "blur" }
        ],
        apkIds: [
            { required: true, message: "APK ID不能为空", trigger: "blur" }
        ],
        checkedPackage: [
            { required: true, message: "请选择是否覆盖原APP", trigger: "change" }
        ]
    }
});

const { queryParams, form, rules } = toRefs(data);
const size = ref("small");

// 新增的响应式数据
const groupList = ref([]);
const deviceList = ref([]);
const apkList = ref([]);
const deviceLoading = ref(false);
const apkLoading = ref(false);

// 设备选择相关
const selectedDeviceGroupId = ref(null);
const selectedDeviceGroupName = ref('');
const selectedDevices = ref([]);

// APK选择相关（与设备组关联）
const selectedApks = ref([]);

// 全选相关状态
const deviceSelectAll = ref(false);
const deviceIndeterminate = ref(false);
const apkSelectAll = ref(false);
const apkIndeterminate = ref(false);

// 时间选择相关
const selectedDate = ref('');
const selectedTime = ref('');
const timeOptions = ref([]);

// 生成时间选项（每30分钟一个选项）
function generateTimeOptions() {
    const options = [];
    for (let hour = 0; hour < 24; hour++) {
        for (let minute = 0; minute < 60; minute += 30) {
            const timeStr = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;
            options.push({
                label: timeStr,
                value: timeStr
            });
        }
    }
    timeOptions.value = options;
}

// 更新推送时间
function updatePushTime() {
    if (selectedDate.value && selectedTime.value) {
        form.value.pushTime = `${selectedDate.value} ${selectedTime.value}:00`;
    }
}

// 监听日期变化
watch(selectedDate, () => {
    updatePushTime();
});

// 禁用过去的日期
const disabledDate = (time) => {
    return time.getTime() < Date.now() - 8.64e7; // 禁用昨天之前的日期
};



// 加载组列表
async function loadGroupList() {
    try {
        const response = await listGroup();
        groupList.value = response.data || response;
    } catch (error) {
        console.error('加载组列表失败:', error);
        groupList.value = [];
    }
}

// 设备组选择变化
async function handleDeviceGroupChange(groupId) {
    selectedDeviceGroupId.value = groupId;
    const selectedGroup = groupList.value.find(group => group.id === groupId);
    selectedDeviceGroupName.value = selectedGroup ? selectedGroup.groupName : '';

    // 清空之前选择的设备和APK
    selectedDevices.value = [];
    selectedApks.value = [];

    // 重置全选状态
    deviceSelectAll.value = false;
    deviceIndeterminate.value = false;
    apkSelectAll.value = false;
    apkIndeterminate.value = false;

    if (groupId) {
        // 同时加载设备列表和APK列表
        await Promise.all([
            loadDeviceList(groupId),
            loadApkList(groupId)
        ]);
    } else {
        deviceList.value = [];
        apkList.value = [];
    }
}

// 加载设备列表
async function loadDeviceList(groupId) {
    deviceLoading.value = true;
    try {
        const response = await listDevice({ groupId });
        deviceList.value = response.rows || response.data || [];
    } catch (error) {
        console.error('加载设备列表失败:', error);
        deviceList.value = [];
    } finally {
        deviceLoading.value = false;
    }
}

// 设备选择变化
function handleDeviceSelectionChange(selectedDeviceIds) {
    selectedDevices.value = selectedDeviceIds;
    // 更新表单数据
    form.value.deviceIds = selectedDeviceIds.join(',');
    // 更新全选状态
    updateDeviceSelectAllStatus();
}

// 设备全选处理
function handleDeviceSelectAll(checked) {
    if (checked) {
        selectedDevices.value = deviceList.value.map(device => device.id);
    } else {
        selectedDevices.value = [];
    }
    form.value.deviceIds = selectedDevices.value.join(',');
    updateDeviceSelectAllStatus();
}

// 更新设备全选状态
function updateDeviceSelectAllStatus() {
    const selectedCount = selectedDevices.value.length;
    const totalCount = deviceList.value.length;

    deviceSelectAll.value = selectedCount === totalCount && totalCount > 0;
    deviceIndeterminate.value = selectedCount > 0 && selectedCount < totalCount;
}



// 加载APK列表
async function loadApkList(groupId) {
    apkLoading.value = true;
    try {
        const response = await getGroupApk(groupId);
        apkList.value = response.data || [];
    } catch (error) {
        console.error('加载APK列表失败:', error);
        apkList.value = [];
    } finally {
        apkLoading.value = false;
    }
}

// APK选择变化
function handleApkSelectionChange(selectedApkIds) {
    selectedApks.value = selectedApkIds;
    // 更新表单数据
    form.value.apkIds = selectedApkIds.join(',');
    // 更新全选状态
    updateApkSelectAllStatus();
}

// APK全选处理
function handleApkSelectAll(checked) {
    if (checked) {
        selectedApks.value = apkList.value.map(apk => apk.id);
    } else {
        selectedApks.value = [];
    }
    form.value.apkIds = selectedApks.value.join(',');
    updateApkSelectAllStatus();
}

// 更新APK全选状态
function updateApkSelectAllStatus() {
    const selectedCount = selectedApks.value.length;
    const totalCount = apkList.value.length;

    apkSelectAll.value = selectedCount === totalCount && totalCount > 0;
    apkIndeterminate.value = selectedCount > 0 && selectedCount < totalCount;
}

// 图片加载错误处理
function handleImageError(event) {
    event.target.src = '/src/assets/images/default-app-icon.png';
}

/** 查询定时推送列表 */
function getList() {
    loading.value = true;
    listJob(queryParams.value).then(response => {
        jobList.value = response.rows;
        total.value = response.total;
        loading.value = false;
    });
}

// 取消按钮
function cancel() {
    open.value = false;
    reset();
}

// 表单重置
function reset() {
    form.value = {
        id: null,
        pushTime: null,
        deviceIds: null,
        apkIds: null,
        checkedPackage: null,
        status: null,
        remark: null,
        createBy: null,
        createTime: null
    };

    // 重置选择状态
    selectedDeviceGroupId.value = null;
    selectedDeviceGroupName.value = '';
    selectedDevices.value = [];
    selectedApks.value = [];
    deviceList.value = [];
    apkList.value = [];
    selectedDate.value = '';
    selectedTime.value = '';

    // 重置全选状态
    deviceSelectAll.value = false;
    deviceIndeterminate.value = false;
    apkSelectAll.value = false;
    apkIndeterminate.value = false;

    proxy.resetForm("jobRef");
}

/** 搜索按钮操作 */
function handleQuery() {
    queryParams.value.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
    ids.value = selection.map(item => item.id);
    single.value = selection.length != 1;
    multiple.value = !selection.length;
}

/** 新增按钮操作 */
async function handleAdd() {
    reset();
    await loadGroupList();
    generateTimeOptions();
    open.value = true;
    title.value = "添加定时推送";
}

/** 复制新增操作 */
async function handleCopy(row) {
    reset();
    await loadGroupList();
    generateTimeOptions();

    // 复制基本信息
    form.value = {
        id: null,
        pushTime: null, // 时间需要重新选择
        deviceIds: row.deviceIds,
        apkIds: row.apkIds,
        checkedPackage: row.checkedPackage,
        status: null,
        remark: row.remark,
        createBy: null,
        createTime: null
    };

    // 如果有设备ID，尝试解析并设置选择状态
    if (row.deviceIds) {
        const deviceIdArray = row.deviceIds.split(',').map(id => parseInt(id));
        selectedDevices.value = deviceIdArray;

        // 尝试找到设备所属的组（假设所有设备都在同一组）
        // 这里需要通过API查询设备信息来确定组ID
        await loadDeviceGroupFromDeviceIds(deviceIdArray);
    }

    // 如果有APK ID，设置选择状态
    if (row.apkIds) {
        const apkIdArray = row.apkIds.split(',').map(id => parseInt(id));
        selectedApks.value = apkIdArray;
    }

    // 更新全选状态
    updateDeviceSelectAllStatus();
    updateApkSelectAllStatus();

    open.value = true;
    title.value = "复制定时推送";
}

// 根据设备ID查找设备组
async function loadDeviceGroupFromDeviceIds(deviceIds) {
    try {
        // 获取所有组
        const groupResponse = await listGroup();
        const groups = groupResponse.data || groupResponse;

        // 遍历组，找到包含这些设备的组
        for (const group of groups) {
            const deviceResponse = await listDevice({ groupId: group.id });
            const devices = deviceResponse.rows || deviceResponse.data || [];

            // 检查是否有匹配的设备
            const hasMatchingDevice = devices.some(device => deviceIds.includes(device.id));
            if (hasMatchingDevice) {
                selectedDeviceGroupId.value = group.id;
                selectedDeviceGroupName.value = group.groupName;
                deviceList.value = devices;

                // 同时加载该组的APK
                await loadApkList(group.id);
                break;
            }
        }
    } catch (error) {
        console.error('查找设备组失败:', error);
    }
}

/** 修改按钮操作 */
function handleUpdate(row) {
    reset();
    const _id = row.id || ids.value
    getJob(_id).then(response => {
        form.value = response.data;
        open.value = true;
        title.value = "修改定时推送";
    });
}

/** 提交按钮 */
function submitForm() {
    proxy.$refs["jobRef"].validate(valid => {
        if (valid) {
            if (form.value.id != null) {
                updateJob(form.value).then(() => {
                    proxy.$modal.msgSuccess("修改成功");
                    open.value = false;
                    getList();
                });
            } else {
                addJob(form.value).then(() => {
                    proxy.$modal.msgSuccess("新增成功");
                    open.value = false;
                    getList();
                });
            }
        }
    });
}

/** 删除按钮操作 */
function handleDelete(row) {
    const _ids = row.id || ids.value;
    proxy.$modal.confirm('是否确认删除定时推送编号为"' + _ids + '"的数据项？').then(function () {
        return delJob(_ids);
    }).then(() => {
        getList();
        proxy.$modal.msgSuccess("删除成功");
    }).catch(() => { });
}

/** 导出按钮操作 */
function handleExport() {
    proxy.download('job/job/export', {
        ...queryParams.value
    }, `job_${new Date().getTime()}.xlsx`)
}

getList();
</script>
<style lang="scss" scoped>
.lf-table-content-height {
    height: calc(100vh - 260px) !important;
}

:deep(.el-table__inner-wrapper:before) {
    height: 0.8px !important;
}

.selection-container {
    display: flex;
    gap: 20px;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    padding: 15px;
    background-color: #fafafa;
}

.selection-left {
    flex: 1;
    min-width: 200px;
}

.selection-right {
    flex: 2;
    min-width: 300px;
}



.selected-info {
    padding: 8px 12px;
    background-color: #e1f3d8;
    border: 1px solid #b3d8a4;
    border-radius: 4px;
    color: #67c23a;
    font-size: 12px;
}

.group-info {
    padding: 12px;
    background-color: #f0f9ff;
    border: 1px solid #bfdbfe;
    border-radius: 6px;
}

.info-item {
    display: flex;
    margin-bottom: 8px;
}

.info-item:last-child {
    margin-bottom: 0;
}

.info-item .label {
    font-weight: 500;
    color: #374151;
    min-width: 80px;
}

.info-item .value {
    color: #1f2937;
    font-weight: 600;
}

.selection-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.selection-header h4 {
    margin: 0;
    color: #303133;
    font-size: 14px;
    font-weight: 600;
}

.selection-actions {
    display: flex;
    align-items: center;
}

.device-list,
.apk-list {
    height: 300px;
    overflow-y: auto;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    padding: 12px;
    background-color: white;
}

.device-list::-webkit-scrollbar,
.apk-list::-webkit-scrollbar {
    width: 6px;
}

.device-list::-webkit-scrollbar-track,
.apk-list::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.device-list::-webkit-scrollbar-thumb,
.apk-list::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.device-list::-webkit-scrollbar-thumb:hover,
.apk-list::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

.device-item,
.apk-item {
    margin-bottom: 8px;
    padding: 8px;
    border-radius: 4px;
    transition: background-color 0.2s;
}

.device-item:hover,
.apk-item:hover {
    background-color: #f5f7fa;
}

.device-info {
    margin-left: 8px;
}

.device-name {
    font-weight: 500;
    color: #303133;
    font-size: 13px;
}

.device-detail {
    color: #909399;
    font-size: 11px;
    margin-top: 2px;
}

.apk-info {
    display: flex;
    align-items: center;
    margin-left: 8px;
}

.apk-icon {
    width: 32px;
    height: 32px;
    border-radius: 4px;
    margin-right: 10px;
    object-fit: cover;
}

.apk-detail {
    flex: 1;
}

.apk-name {
    font-weight: 500;
    color: #303133;
    font-size: 13px;
}

.apk-package {
    color: #909399;
    font-size: 11px;
    margin-top: 2px;
}

.empty-tip {
    text-align: center;
    color: #909399;
    font-size: 12px;
    padding: 20px;
}

:deep(.el-checkbox-group) {
    display: block;
}

:deep(.el-checkbox) {
    display: block;
    margin-right: 0;
    margin-bottom: 0;
}

:deep(.el-checkbox__label) {
    width: 100%;
}
</style>
