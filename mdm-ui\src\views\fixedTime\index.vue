<template>
    <div class="lf-content">
        <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="124px">
            <el-form-item label="推送时间" prop="pushTime">
                <el-date-picker clearable v-model="queryParams.pushTime" type="date" value-format="YYYY-MM-DD"
                    placeholder="请选择推送时间" @keyup.enter="handleQuery">
                </el-date-picker>
            </el-form-item>
            <el-form-item label="是否覆盖原APP" prop="checkedPackage" >
                <el-select v-model="queryParams.checkedPackage" placeholder="是否覆盖原APP" clearable
                    @change="handleQuery" style="width: 200px;">
                    <el-option label="不覆盖" value="0" />
                    <el-option label="覆盖" value="1" />
                </el-select>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
                <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain icon="Plus" @click="handleAdd"
                    v-hasPermi="['job:job:add']">新增</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate"
                    v-hasPermi="['job:job:edit']">修改</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete"
                    v-hasPermi="['job:job:remove']">删除</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="warning" plain icon="Download" @click="handleExport"
                    v-hasPermi="['job:job:export']">导出</el-button>
            </el-col>
            <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>

        <el-table v-loading="loading" :data="jobList" @selection-change="handleSelectionChange" :size="size" border
            class="lf-table-content-height">
            <el-table-column type="selection" width="55" align="center" />
            <el-table-column type="index" label="序号" width="50" align="center" show-overflow-tooltip />
            <el-table-column label="推送时间" align="center" prop="pushTime" width="180" show-overflow-tooltip sortable>
                <template #default="scope">
                    <span>{{ parseTime(scope.row.pushTime, '{y}-{m}-{d} {h}:{i}') }}</span>
                </template>
            </el-table-column>
            <el-table-column label="所选设备" align="center" prop="deviceIds" width="150" show-overflow-tooltip sortable />
            <el-table-column label="推送APK" align="center" prop="apkIds" width="150" show-overflow-tooltip sortable />
            <el-table-column label="是否覆盖原APP" align="center" prop="checkedPackage" width="120" show-overflow-tooltip>
                <template #default="scope">
                    <span>{{ scope.row.checkedPackage === 1 ? '覆盖' : (scope.row.checkedPackage === 0 ? '不覆盖' : '未知')
                        }}</span>
                </template>
            </el-table-column>
            <el-table-column label="推送状态" align="center" prop="status" width="100" show-overflow-tooltip>
                <template #default="scope">
                    <span>{{ scope.row.status === 0 ? '待执行' : (scope.row.status === 1 ? '已执行' : '执行失败') }}</span>
                </template>
            </el-table-column>
            <el-table-column label="备注" align="center" prop="remark" show-overflow-tooltip />
            <el-table-column label="操作" align="center" class-name="small-padding fixed-width" fixed="right"
                show-overflow-tooltip>
                <template #default="scope">
                    <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)"
                        v-hasPermi="['job:job:edit']">修改</el-button>
                    <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)"
                        v-hasPermi="['job:job:remove']">删除</el-button>
                </template>
            </el-table-column>
        </el-table>
        <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
            v-model:limit="queryParams.pageSize" @pagination="getList" />

        <!-- 添加或修改定时推送对话框 -->
        <el-dialog :title="title" v-model="open" width="500px" append-to-body>
            <el-form ref="jobRef" :model="form" :rules="rules" label-width="120px">
                <el-form-item label="定时任务推送时间" prop="pushTime">
                    <el-date-picker clearable v-model="form.pushTime" type="datetime" value-format="YYYY-MM-DD HH:mm:ss"
                        format="YYYY-MM-DD HH:mm" placeholder="请选择推送时间" :default-time="new Date(2000, 1, 1, 0, 0, 0)"
                        :disabled-date="disabledDate" :disabled-hours="() => []" :disabled-minutes="disabledMinutes"
                        :disabled-seconds="() => [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59]"
                        style="width: 100%;">
                    </el-date-picker>
                </el-form-item>
                <el-form-item label="所有需要推送的设备id使用,间隔" prop="deviceIds">
                    <el-input v-model="form.deviceIds" type="textarea" placeholder="请输入内容" />
                </el-form-item>
                <el-form-item label="选中apk的所有id使用，间隔" prop="apkIds">
                    <el-input v-model="form.apkIds" type="textarea" placeholder="请输入内容" />
                </el-form-item>
                <el-form-item label="是否覆盖原APP" prop="checkedPackage">
                    <el-select v-model="form.checkedPackage" placeholder="请选择是否覆盖原APP" style="width: 100%;">
                        <el-option label="不覆盖" value="0" />
                        <el-option label="覆盖" value="1" />
                    </el-select>
                </el-form-item>
                <el-form-item label="备注" prop="remark">
                    <el-input v-model="form.remark" placeholder="请输入备注" />
                </el-form-item>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" @click="submitForm">确 定</el-button>
                    <el-button @click="cancel">取 消</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script setup name="Job">
import { listJob, getJob, delJob, addJob, updateJob } from "@/api/job/index.js";

const { proxy } = getCurrentInstance();

const jobList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

const data = reactive({
    form: {},
    queryParams: {
        pageNum: 1,
        pageSize: 20,
        pushTime: null,
        deviceIds: null,
        apkIds: null,
        checkedPackage: null,
        status: null,
    },
    rules: {
        pushTime: [
            { required: true, message: "推送时间不能为空", trigger: "blur" }
        ],
        deviceIds: [
            { required: true, message: "设备ID不能为空", trigger: "blur" }
        ],
        apkIds: [
            { required: true, message: "APK ID不能为空", trigger: "blur" }
        ],
        checkedPackage: [
            { required: true, message: "请选择是否覆盖原APP", trigger: "change" }
        ]
    }
});

const { queryParams, form, rules } = toRefs(data);
const size = ref("small");

// 禁用过去的日期
const disabledDate = (time) => {
    return time.getTime() < Date.now() - 8.64e7; // 禁用昨天之前的日期
};

// 禁用分钟，只允许00和30分钟
const disabledMinutes = () => {
    const minutes = [];
    for (let i = 0; i < 60; i++) {
        if (i !== 0 && i !== 30) {
            minutes.push(i);
        }
    }
    return minutes;
};

/** 查询定时推送列表 */
function getList() {
    loading.value = true;
    listJob(queryParams.value).then(response => {
        jobList.value = response.rows;
        total.value = response.total;
        loading.value = false;
    });
}

// 取消按钮
function cancel() {
    open.value = false;
    reset();
}

// 表单重置
function reset() {
    form.value = {
        id: null,
        pushTime: null,
        deviceIds: null,
        apkIds: null,
        checkedPackage: null,
        status: null,
        remark: null,
        createBy: null,
        createTime: null
    };
    proxy.resetForm("jobRef");
}

/** 搜索按钮操作 */
function handleQuery() {
    queryParams.value.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
    ids.value = selection.map(item => item.id);
    single.value = selection.length != 1;
    multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
    reset();
    open.value = true;
    title.value = "添加定时推送";
}

/** 修改按钮操作 */
function handleUpdate(row) {
    reset();
    const _id = row.id || ids.value
    getJob(_id).then(response => {
        form.value = response.data;
        open.value = true;
        title.value = "修改定时推送";
    });
}

/** 提交按钮 */
function submitForm() {
    proxy.$refs["jobRef"].validate(valid => {
        if (valid) {
            if (form.value.id != null) {
                updateJob(form.value).then(() => {
                    proxy.$modal.msgSuccess("修改成功");
                    open.value = false;
                    getList();
                });
            } else {
                addJob(form.value).then(() => {
                    proxy.$modal.msgSuccess("新增成功");
                    open.value = false;
                    getList();
                });
            }
        }
    });
}

/** 删除按钮操作 */
function handleDelete(row) {
    const _ids = row.id || ids.value;
    proxy.$modal.confirm('是否确认删除定时推送编号为"' + _ids + '"的数据项？').then(function () {
        return delJob(_ids);
    }).then(() => {
        getList();
        proxy.$modal.msgSuccess("删除成功");
    }).catch(() => { });
}

/** 导出按钮操作 */
function handleExport() {
    proxy.download('job/job/export', {
        ...queryParams.value
    }, `job_${new Date().getTime()}.xlsx`)
}

getList();
</script>
<style lang="scss" scoped>
.lf-table-content-height {
    height: calc(100vh - 260px) !important;
}

:deep(.el-table__inner-wrapper:before) {
    height: 0.8px !important;
}
</style>
