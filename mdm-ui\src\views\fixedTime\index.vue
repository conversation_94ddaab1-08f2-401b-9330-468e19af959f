<template>
    <div class="app-container">
        <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="114px">
            <el-form-item label="推送时间" prop="pushTime">
                <el-date-picker clearable v-model="queryParams.pushTime" type="date" value-format="YYYY-MM-DD"
                    placeholder="请选择推送时间">
                </el-date-picker>
            </el-form-item>
            <el-form-item label="是否覆盖原APP" prop="checkedPackage">
                <el-input v-model="queryParams.checkedPackage" placeholder="请输入0不覆盖，1覆盖" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item>
                <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
                <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain icon="Plus" @click="handleAdd"
                    v-hasPermi="['job:job:add']">新增</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate"
                    v-hasPermi="['job:job:edit']">修改</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete"
                    v-hasPermi="['job:job:remove']">删除</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="warning" plain icon="Download" @click="handleExport"
                    v-hasPermi="['job:job:export']">导出</el-button>
            </el-col>
            <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>

        <el-table v-loading="loading" :data="jobList" @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" align="center" />
            <el-table-column type="index" label="序号" width="55" />
            <el-table-column label="推送时间" align="center" prop="pushTime" width="120">
                <template #default="scope">
                    <span>{{ parseTime(scope.row.pushTime, '{y}-{m}-{d}') }}</span>
                </template>
            </el-table-column>
            <el-table-column label="所选设备" align="center" prop="deviceIds" />
            <el-table-column label="推送APK" align="center" prop="apkIds" />
            <el-table-column label="是否覆盖原APP" align="center" prop="checkedPackage" />
            <!-- 0待执行，1已执行，2执行失败 -->
            <el-table-column label="推送状态" align="center" prop="status" />
            <el-table-column label="备注" align="center" prop="remark" />
            <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                <template #default="scope">
                    <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)"
                        v-hasPermi="['job:job:edit']">修改</el-button>
                    <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)"
                        v-hasPermi="['job:job:remove']">删除</el-button>
                </template>
            </el-table-column>
        </el-table>
        <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
            v-model:limit="queryParams.pageSize" @pagination="getList" />

        <!-- 添加或修改定时推送对话框 -->
        <el-dialog :title="title" v-model="open" width="500px" append-to-body>
            <el-form ref="jobRef" :model="form" :rules="rules" label-width="80px">
                <el-form-item label="推送时间" prop="pushTime">
                    <el-date-picker clearable v-model="form.pushTime" type="date" value-format="YYYY-MM-DD"
                        placeholder="请选择推送时间">
                    </el-date-picker>
                </el-form-item>
                <el-form-item label="所有需要推送的设备id使用,间隔" prop="deviceIds">
                    <el-input v-model="form.deviceIds" type="textarea" placeholder="请输入内容" />
                </el-form-item>
                <el-form-item label="选中apk的所有id使用，间隔" prop="apkIds">
                    <el-input v-model="form.apkIds" type="textarea" placeholder="请输入内容" />
                </el-form-item>
                <el-form-item label="0不覆盖，1覆盖" prop="checkedPackage">
                    <el-input v-model="form.checkedPackage" placeholder="请输入0不覆盖，1覆盖" />
                </el-form-item>
                <el-form-item label="备注" prop="remark">
                    <el-input v-model="form.remark" placeholder="请输入备注" />
                </el-form-item>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" @click="submitForm">确 定</el-button>
                    <el-button @click="cancel">取 消</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script setup name="Job">
import { listJob, getJob, delJob, addJob, updateJob } from "@/api/job/index.js";

const { proxy } = getCurrentInstance();

const jobList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

const data = reactive({
    form: {},
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        pushTime: null,
        deviceIds: null,
        apkIds: null,
        checkedPackage: null,
        status: null,
    },
    rules: {
    }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询定时推送列表 */
function getList() {
    loading.value = true;
    listJob(queryParams.value).then(response => {
        jobList.value = response.rows;
        total.value = response.total;
        loading.value = false;
    });
}

// 取消按钮
function cancel() {
    open.value = false;
    reset();
}

// 表单重置
function reset() {
    form.value = {
        id: null,
        pushTime: null,
        deviceIds: null,
        apkIds: null,
        checkedPackage: null,
        status: null,
        remark: null,
        createBy: null,
        createTime: null
    };
    proxy.resetForm("jobRef");
}

/** 搜索按钮操作 */
function handleQuery() {
    queryParams.value.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
    ids.value = selection.map(item => item.id);
    single.value = selection.length != 1;
    multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
    reset();
    open.value = true;
    title.value = "添加定时推送";
}

/** 修改按钮操作 */
function handleUpdate(row) {
    reset();
    const _id = row.id || ids.value
    getJob(_id).then(response => {
        form.value = response.data;
        open.value = true;
        title.value = "修改定时推送";
    });
}

/** 提交按钮 */
function submitForm() {
    proxy.$refs["jobRef"].validate(valid => {
        if (valid) {
            if (form.value.id != null) {
                updateJob(form.value).then(response => {
                    proxy.$modal.msgSuccess("修改成功");
                    open.value = false;
                    getList();
                });
            } else {
                addJob(form.value).then(response => {
                    proxy.$modal.msgSuccess("新增成功");
                    open.value = false;
                    getList();
                });
            }
        }
    });
}

/** 删除按钮操作 */
function handleDelete(row) {
    const _ids = row.id || ids.value;
    proxy.$modal.confirm('是否确认删除定时推送编号为"' + _ids + '"的数据项？').then(function () {
        return delJob(_ids);
    }).then(() => {
        getList();
        proxy.$modal.msgSuccess("删除成功");
    }).catch(() => { });
}

/** 导出按钮操作 */
function handleExport() {
    proxy.download('job/job/export', {
        ...queryParams.value
    }, `job_${new Date().getTime()}.xlsx`)
}

getList();
</script>
