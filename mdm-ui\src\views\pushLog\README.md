# 推送日志页面 (pushLog)

## 📋 页面概述

推送日志页面用于查看和管理APK推送任务的执行记录，提供详细的推送统计信息和执行状态。

## ✅ 按照fixedTime风格的改进

### 🎨 界面优化

**1. 表格样式改进**
- 添加边框和小尺寸样式
- 优化列宽和对齐方式
- 添加序号列和排序功能
- 固定操作列到右侧

**2. 时间显示优化**
- 统一时间格式：`{y}-{m}-{d} {h}:{i}:{s}`
- 添加sortable排序功能
- 优化列宽为150px

**3. 数据展示改进**
- 设备ID和APK ID改为可点击的查看按钮
- 显示数量统计：`查看设备 (3)` / `查看APK (2)`
- 任务完成量使用进度条显示

### 🔧 功能增强

**1. 设备详情查看**
```vue
<el-table-column label="所选设备" align="center" width="120" show-overflow-tooltip>
    <template #default="scope">
        <el-button link type="primary" @click="showDeviceDetails(scope.row)">
            查看设备 ({{ scope.row.deviceIds ? scope.row.deviceIds.split(',').length : 0 }})
        </el-button>
    </template>
</el-table-column>
```

```javascript
/** 显示设备详情 */
async function showDeviceDetails(row) {
    try {
        // 调用API获取设备详情
        const response = await getDeviceByPushLogId(row.id);
        if (response.data && response.data.length > 0) {
            deviceDetailList.value = response.data;
            deviceDetailOpen.value = true;
        } else {
            proxy.$modal.msgWarning("未找到该推送日志的设备信息");
        }
    } catch (error) {
        console.error('获取设备详情失败:', error);
        proxy.$modal.msgError("获取设备详情失败");
    }
}
```

**2. APK详情查看**
```vue
<el-table-column label="推送APK" align="center" width="120" show-overflow-tooltip>
    <template #default="scope">
        <el-button link type="primary" @click="showApkDetails(scope.row)">
            查看APK ({{ scope.row.apkIds ? scope.row.apkIds.split(',').length : 0 }})
        </el-button>
    </template>
</el-table-column>
```

```javascript
/** 显示APK详情 */
async function showApkDetails(row) {
    try {
        // 调用API获取APK详情
        const response = await getPushApkByPushLogId(row.id);
        if (response.data && response.data.length > 0) {
            apkDetailList.value = response.data;
            apkDetailOpen.value = true;
        } else {
            proxy.$modal.msgWarning("未找到该推送日志的APK信息");
        }
    } catch (error) {
        console.error('获取APK详情失败:', error);
        proxy.$modal.msgError("获取APK详情失败");
    }
}
```

**3. 进度条显示**
```vue
<el-table-column label="任务完成量" align="center" width="100" show-overflow-tooltip>
    <template #default="scope">
        <el-progress :percentage="parseFloat(scope.row.completionPercentage || 0)"
            :color="getProgressColor(scope.row.completionPercentage)" />
    </template>
</el-table-column>
```

**4. 剩余APK数量查看和推送**
```vue
<el-table-column label="剩余APK数" align="center" width="100" show-overflow-tooltip>
    <template #default="scope">
        <el-button link type="primary" @click="showRemainingDevices(scope.row)">
            {{ scope.row.remainingCount || 0 }}
        </el-button>
    </template>
</el-table-column>
```

**剩余设备对话框功能：**
- **全选功能**：支持全选/取消全选在线设备
- **批量推送**：循环调用sendMessageToAndroid，遍历每个设备单独推送
- **单个推送**：每行都有推送按钮，支持单个设备推送
- **状态控制**：只有在线设备可以选择和推送
- **结果统计**：批量推送时统计成功和失败的设备数量

```vue
<!-- 操作按钮区域 -->
<div class="remaining-device-actions">
    <el-button type="primary" @click="handleSelectAll" size="small">
        {{ isAllSelected ? '取消全选' : '全选' }}
    </el-button>
    <el-button type="success" @click="handleBatchPush" :disabled="selectedRemainingDevices.length === 0" size="small">
        批量推送 ({{ selectedRemainingDevices.length }})
    </el-button>
</div>

<!-- 表格选择列和操作列 -->
<el-table-column type="selection" width="50" align="center" :selectable="checkDeviceSelectable" />
<el-table-column label="操作" width="100" align="center" fixed="right">
    <template #default="scope">
        <el-button type="primary" size="small" @click="handleSinglePush(scope.row)"
            :disabled="scope.row.status !== 1">
            推送
        </el-button>
    </template>
</el-table-column>
```

**循环推送实现：**
```javascript
// 单个设备推送
await sendMessageToAndroid({
    deviceIds: [device.id],
    type: "server",
    value: "download",
    checkedPackage: true,
    apkIds: device.apkList.map(apk => apk.id),
    isAllGroup: false
});

// 批量推送 - 循环遍历每个设备
const pushPromises = selectedRemainingDevices.value.map(device => {
    const apkIds = device.apkList ? device.apkList.map(apk => apk.id) : [];

    return sendMessageToAndroid({
        deviceIds: [device.id],
        type: "server",
        value: "download",
        checkedPackage: true,
        apkIds: apkIds,
        isAllGroup: false
    }).then(res => {
        console.log(`设备 ${device.deviceName || device.sn} 推送结果:`, res);
        return { device, success: true, result: res };
    }).catch(error => {
        console.error(`设备 ${device.deviceName || device.sn} 推送失败:`, error);
        return { device, success: false, error };
    });
});

// 等待所有推送完成并统计结果
const results = await Promise.all(pushPromises);
const successCount = results.filter(r => r.success).length;
const failCount = results.filter(r => !r.success).length;
```

### 📊 详情对话框

**1. 设备详情对话框**
- 显示设备型号、SN、IP、MAC地址、所属组
- 800px宽度，固定高度
- 支持滚动查看

**2. APK详情对话框**
- 显示APK图标、名称、包名、版本
- 图标加载错误处理
- 统一样式设计

### 🎨 样式特性

**1. 表格样式**
- 圆角边框设计
- 悬停效果
- 表头背景色区分

**2. 进度条样式**
- 动态颜色：绿色(90%+)、橙色(70%+)、红色(50%+)、灰色(<50%)
- 圆角设计
- 平滑过渡效果

**3. 对话框样式**
- 固定高度和滚动
- 统一的表格样式
- 响应式设计

## 🔄 数据流程

1. **列表展示**：显示推送日志基本信息
2. **详情查看**：点击设备/APK数量查看详细信息
3. **进度展示**：直观显示任务完成进度
4. **操作管理**：支持修改和删除操作

## 📝 技术实现

- **Vue 3 Composition API**：现代化的组件开发
- **Element Plus**：统一的UI组件库
- **响应式数据**：实时更新的数据展示
- **SCSS样式**：模块化的样式管理

### 🔗 API集成

**1. 设备详情API**
- **接口名称**：`getDeviceByPushLogId(pushLogId)`
- **功能**：根据推送日志ID获取相关设备详情
- **返回数据**：设备列表，包含型号、SN、IP、MAC、所属组等信息

**2. APK详情API**
- **接口名称**：`getPushApkByPushLogId(pushLogId)`
- **功能**：根据推送日志ID获取相关APK详情
- **返回数据**：APK列表，包含图标、名称、包名、版本等信息

**3. 剩余设备和APK API**
- **接口名称**：`getRemainingDeviceAndApk(pushLogId)`
- **功能**：根据推送日志ID获取未安装APK的设备列表
- **返回数据**：设备列表，每个设备包含未安装的APK列表

**4. 数据字段映射**
- **设备字段**：`model`, `sn`, `ip`, `mac`, `groupName`
- **APK字段**：`apkName`, `apkPackage`, `version`, `apkImg`/`apkImage`
- **剩余设备字段**：`deviceSn`, `deviceName`, `deviceModel`, `deviceIp`, `apkList`

## 🎯 用户体验

- **直观展示**：一目了然的数据统计
- **便捷操作**：一键查看详细信息
- **真实数据**：通过API获取准确的设备和APK信息
- **视觉反馈**：进度条和颜色编码
- **错误处理**：友好的错误提示和异常处理
- **响应式设计**：适配不同屏幕尺寸
