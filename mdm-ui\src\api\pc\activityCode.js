import request from '@/utils/request'

// 查询注册码列表
export function listActivityCode(query) {
  return request({
    url: '/pc/activityCode/list',
    method: 'get',
    params: query
  })
}

// 查询注册码详细
export function getActivityCode(id) {
  return request({
    url: '/pc/activityCode/' + id,
    method: 'get'
  })
}

// 新增注册码
export function addActivityCode(data) {
  return request({
    url: '/pc/activityCode',
    method: 'post',
    data: data
  })
}

// 修改注册码
export function updateActivityCode(data) {
  return request({
    url: '/pc/activityCode',
    method: 'put',
    data: data
  })
}

// 删除注册码
export function delActivityCode(id) {
  return request({
    url: '/pc/activityCode/' + id,
    method: 'delete'
  })
}
