<template>
  <div class="lf-content index">
    <!-- 数据中心 -->
    <el-card style="max-width: 100%" :style="{ boxShadow: `var(${getCssVarName('dark')})` }">
      <template #header>
        <div class="card-header">
          <span>设备数据</span>
        </div>
      </template>
      <div class="data">
        <div class="board">
          <div class="item item1">
            <div class="statistic-card">
              <el-statistic :value="data.onLine[1]" @click="toDevice">
                <template #title>
                  <div style=" display: inline-flex; align-items: center; color: white; ">
                    设备在线数量
                    <el-tooltip effect="dark" content="设备当前在线总数量" placement="top">
                      <el-icon style="margin-left: 4px" :size="12">
                        <Warning />
                      </el-icon>
                    </el-tooltip>
                  </div>
                </template>
              </el-statistic>
              <div class="statistic-footer">
                <div class="footer-item">
                  <span>设备总数量</span>
                  <span class="green">
                    {{ data.onLine[0] }}
                  </span>
                </div>
              </div>
            </div>
            <!-- <el-progress :percentage="70" :stroke-width="3" style="margin-left: 10px;" striped /> -->
          </div>
          <div class="item item2">
            <div class="statistic-card">
              <el-statistic :value="data.licence[0]" style="color: #67c23a !important" @click="toDeviceLicence">
                <template #title>
                  <div style="
                      display: inline-flex;
                      align-items: center;
                      color: white;
                    ">
                    Licence使用量
                    <el-tooltip effect="dark" content="Licence使用量及占比" placement="top">
                      <el-icon style="margin-left: 4px" :size="12">
                        <Warning />
                      </el-icon>
                    </el-tooltip>
                  </div>
                </template>
              </el-statistic>
              <el-progress :percentage="Math.floor((data.licence[0] / data.licence[1]) * 100)" :stroke-width="3"
                style="margin-left: 0px" striped />
              <div class="statistic-footer">
                <div class="footer-item">
                  <span>lisence总数量</span>
                  <span class="green">
                    {{ data.licence[1] }}
                  </span>
                </div>
              </div>
            </div>
          </div>
          <div class="item item3">
            <div class="statistic-card">
              <el-statistic :value="data.lowPower[1]" style="color: #67c23a !important" @click="toDeviceLowerBattery">
                <template #title>
                  <div style="
                      display: inline-flex;
                      align-items: center;
                      color: white;
                    ">
                    设备低电量
                    <el-tooltip effect="dark" content="设备电量低于30%总量及占比" placement="top">
                      <el-icon style="margin-left: 4px" :size="12">
                        <Warning />
                      </el-icon>
                    </el-tooltip>
                  </div>
                </template>
              </el-statistic>
              <div class="statistic-footer">
                <div class="footer-item">
                  <!-- <span>较昨日</span>
                  <span class="green">
                    1.2%
                    <el-icon>
                      <CaretTop />
                    </el-icon>
                  </span> -->
                </div>
              </div>
            </div>
            <el-progress :percentage="Math.floor((data.lowPower[1] / data.lowPower[0]) * 100)
              " :stroke-width="5" style="margin: -10px 0px 0px 15px" striped />
          </div>
          <div class="item item4">
            <div class="statistic-card">
              <el-statistic :value="data.todayApkPush" style="color: #67c23a !important">
                <template #title>
                  <div style=" display: inline-flex;
                      align-items: center;
                      color: white;
                    ">
                    当前推送任务完成情况
                    <el-tooltip effect="dark" content="当前推送任务完成情况" placement="top">
                      <el-icon style="margin-left: 4px" :size="12">
                        <Warning />
                      </el-icon>
                    </el-tooltip>
                  </div>
                </template>
              </el-statistic>
              <div class="statistic-footer">
                <div class="footer-item">
                  <!-- <span>较昨日</span>
                  <span class="red">
                    1.2%
                  </span> -->
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="footer-content" style="width: 100%; height: 100%;display: flex;">
          <div class="chart" ref="chart" style="flex: 2;margin-top: 20px;"></div>
          <div class="pie" ref="pie" style="flex: 1;margin-top: 20px;">

          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>
<script setup>
import * as echarts from "echarts/core";
import {
  GridComponent,
  TooltipComponent,
  LegendComponent,
  TitleComponent,
} from "echarts/components";
import { BarChart, PieChart } from "echarts/charts";
import { CanvasRenderer } from "echarts/renderers";
echarts.use([
  GridComponent,
  BarChart,
  CanvasRenderer,
  TooltipComponent,
  LegendComponent,
  TitleComponent,
  PieChart
]);

import { getChartData, getGroupChart, getTodayPushNum } from "@/api/chart.js";
import { onBeforeUnmount, onMounted, ref } from "vue";
import { Splitpanes, Pane } from "splitpanes";
import "splitpanes/dist/splitpanes.css";
import router from "@/router/index.js"
const data = reactive({
  onLine: [], // 在线设备数量
  lowPower: [], // 低电量设备数量
  licence: [], // lisence使用量
  todayApkPush: null
});
const pieData = ref([]);
const timer = ref(null);
const chart = ref(null);
const pie = ref(null);
function getList() {
  getChartData().then((res) => {
    // console.log(res);
    data.onLine = res.data.onLine;
    data.lowPower = res.data.lowPower;
    data.licence = res.data.licence;
  });
}
function getGroupList() {
  getGroupChart().then((res) => {
    pieData.value = res.data;
    const { xAxisData, series } = buildStackedData(res.data);
    setchart(xAxisData, series);
    setPie(res.data);
  });
}
function getTodayPush() {
  getTodayPushNum().then((res) => {
    console.log(res.data);
    // 查找 content 字段中包含 ".apk" 的数量
    let apkCount = 0;
    res.data.forEach((item) => {
      apkCount += (item.content.match(/\.apk/g) || []).length;
    });
    data.todayApkPush = apkCount;
  });
};
function buildStackedData(data) {
  // 构建映射
  const idMap = new Map();
  const childrenMap = new Map();
  data.forEach((item) => {
    idMap.set(item.id, item);
    if (!childrenMap.has(item.parentId)) {
      childrenMap.set(item.parentId, []);
    }
    childrenMap.get(item.parentId).push(item);
  });
  // 获取所有子孙 id（含自身）
  function getAllDescendants(id) {
    const result = [id];
    const children = childrenMap.get(id) || [];
    for (const child of children) {
      result.push(...getAllDescendants(child.id));
    }
    return result;
  }
  const xAxisData = data.map((item) => item.groupName);
  const groupIdToIndex = {};
  data.forEach((item, idx) => {
    groupIdToIndex[item.id] = idx;
  });
  // 构造堆积 series（所有子孙 num 累计）
  const stackSeriesMap = new Map();
  for (const group of data) {
    const xIndex = groupIdToIndex[group.id];
    const descendants = getAllDescendants(group.id);
    for (const descId of descendants) {
      const desc = idMap.get(descId);
      if (!stackSeriesMap.has(descId)) {
        stackSeriesMap.set(descId, {
          name: `${desc.groupName}`,
          type: "bar",
          stack: "total-" + group.id,
          barGap: "30%",
          data: Array(data.length).fill(0),
        });
      }
      stackSeriesMap.get(descId).data[xIndex] += desc.num;
    }
  }
  // 找出每列堆积的顶部 series
  const topSeriesTracker = {}; // xIndex => seriesName
  for (let i = 0; i < data.length; i++) {
    let max = 0;
    for (const [key, series] of stackSeriesMap.entries()) {
      const val = series.data[i];
      if (val > 0) {
        topSeriesTracker[i] = series.name;
      }
    }
  }
  // 给堆积 series 设置圆角，非顶部为 [0,0,0,0]，顶部才为 [10,10,0,0]
  for (const [key, series] of stackSeriesMap.entries()) {
    series.data = series.data.map((val, idx) => {
      if (!val) return 0;
      const isTop = topSeriesTracker[idx] === series.name;
      return {
        value: val,
        itemStyle: {
          borderRadius: isTop ? [20, 20, 0, 0] : [0, 0, 0, 0],
        },
      };
    });
  }
  // 构造“自身”柱子 series（非堆积）
  const selfSeries = {
    name: "当前组",
    type: "bar",
    stack: null,
    barGap: "30%",
    itemStyle: {
      color: "#8888ff",
      borderRadius: [10, 10, 0, 0],
    },
    data: data.map((item) =>
      item.num === 0
        ? 0
        : {
          value: item.num,
          itemStyle: {
            borderRadius: [10, 10, 0, 0],
          },
        }
    ),
  };
  return {
    xAxisData,
    series: [...stackSeriesMap.values(), selfSeries],
  };
}
function setchart(xAxisData, series) {
  const myChart = echarts.init(chart.value);
  const option = {
    title: {
      text: "设备分组统计",
      left: "center",
      top: 10,
    },
    legend: {
      type: "scroll",
      orient: "horizontal",
      top: 30,
      left: "center",
      itemWidth: 14,
      itemHeight: 14,
      textStyle: {
        fontSize: 12,
      },
    },
    tooltip: {
      trigger: "axis",
      formatter: function (params) {
        let result = params[0].axisValue + "<br/>";
        params.forEach((item) => {
          const val = item.value;
          if (val && val !== 0 && val !== "-") {
            result += `<span style="display:inline-block;margin-right:5px;border-radius:10px;width:9px;height:9px;background-color:${item.color};"></span>`;
            result += `${item.seriesName}：${val}<br/>`;
          }
        });
        return result;
      },
    },
    grid: {
      top: 60,
      left: 20,
      right: 20,
      bottom: 30,
      containLabel: true,
    },
    xAxis: {
      type: "category",
      data: xAxisData,
      axisLine: {
        show: false, // 隐藏x轴横线
      },
    },
    yAxis: { type: "value" },
    series,
  };
  myChart.setOption(option);
}
function setPie(data) {
  if (!pie.value || !pieData.value || !pieData.value.length) return;
  const myChart = echarts.init(pie.value);
  const option = {
    title: {
      text: "设备分组占比",
      left: "center",
      top: 10,
    },
    tooltip: {
      trigger: "item",
      formatter: "{b}: {c} ({d}%)"
    },
    legend: {
      type: "scroll",
      orient: "vertical",
      left: "left",
      top: 40,
      itemWidth: 14,
      itemHeight: 14,
    },
    series: [
      {
        name: "设备分组",
        type: "pie",
        radius: "60%",
        center: ["60%", "55%"],
        data: data.map(item => ({
          name: item.groupName,
          value: item.num
        })),
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: "rgba(0, 0, 0, 0.5)"
          }
        },
        label: {
          formatter: '{b}: {d}%'
        }
      }
    ]
  };
  myChart.setOption(option);
}
onMounted(() => {
  // timer.value = setInterval(() => {
  //   getList()
  // }, 1000 * 5 * 1) // 每5分钟请求一次数据
  getList();
  getGroupList();
  // getTodayPush(); // 获取今日推送数据
  setchart();
  setPie();
});

onBeforeUnmount(() => {
  console.log("清除定时器");
  // 清除定时器
  if (timer) {
    clearInterval(timer);
  }
});
// 样式
const getCssVarName = (type) => {
  return;
  return `--el-box-shadow${type ? "-" : ""}${type}`;
};

// 页面跳转
// 设备在线数量
function toDevice() {
  router.push({
    path: "/group",
    query: {
      status: 1 // 在线设备
    }
  });
}
// 设备在线数量
function toDeviceLicence() {
  router.push({
    path: "/group",
    query: {
      status: 2 // licence激活设备
    }
  });
}
// 设备低电量
function toDeviceLowerBattery() {
  router.push({
    path: "/group",
    query: {
      batttery: 30 // 低电量设备
    }
  });
}
</script>
<style lang="scss" scoped>
.index {
  width: 100%;
  height: calc(100vh - 84px);
  padding: 10px;

  :deep(.el-card__header) {
    padding: 10px 15px !important;
  }

  .card-header {
    color: black;
    font-weight: 700;
    font-size: 18px;
  }

  :deep(.el-card__body) {
    padding: 10px !important;
  }

  .data {
    width: 100%;
    height: calc(100vh - 174px);
    display: flex;
    flex-direction: column;

    .board {
      width: 100%;
      height: 150px;
      // background-color: #409eff;
      display: flex;
      justify-content: space-around;

      .item {
        width: 24%;
        height: 100%;
        // border: 1px solid #eaeefb;
        border-radius: 8px;
        overflow: hidden;

        .statistic-card {
          height: 80%;
          padding: 15px 0 0 15px;
          border-radius: 4px;
          color: white;
          cursor: pointer;
          // background-color: var(--el-bg-color-overlay);
        }

        .statistic-footer {
          display: flex;
          justify-content: space-between;
          align-items: center;
          flex-wrap: wrap;
          font-size: 12px;
          // color: var(--el-text-color-regular);
          color: var(--el-text-color-white);
          margin-top: 6px;
        }

        .statistic-footer .footer-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          color: white;
          font-size: 14px;
          font-family: 宋体;
        }

        .statistic-footer .footer-item span:last-child {
          display: inline-flex;
          align-items: center;
          margin-left: 4px;
        }

        .green {
          color: var(--el-color-success);
          font-family: 黑体;
          font-size: 16px;
          font-weight: 700;
        }

        .red {
          color: var(--el-color-error);
        }

        :deep(.el-statistic__content) {
          color: white !important;
          font-size: 36px;
        }

        :deep(.el-progress__text) {
          color: rgba(255, 255, 255, 0.605) !important;
        }
      }

      .item1 {
        // background-image: linear-gradient(90deg, #667eea 0%, #764ba2 150%);
        background-image: linear-gradient(-20deg,
            #0c3483 -50%,
            #a2b6df 170%,
            #6b8cce 100%);
        color: white;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
        transform: perspective(500px) rotateX(5deg);
        transition: transform 0.3s;

        :deep(.el-statistic__content) {
          color: white !important;
          font-size: 56px;
        }
      }

      .item1:hover {
        transform: perspective(500px) rotateX(0deg);
      }

      .item2 {
        // background-image: linear-gradient(135deg, #16d9e3 0%, #30c7ec 47%, #46aef7 100%);
        background-image: linear-gradient(-20deg, #09203f -50%, #537895 100%);
        color: white;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
        transform: perspective(500px) rotateX(5deg);
        transition: transform 0.3s;

        :deep(.el-statistic__content) {
          color: white !important;
          font-size: 48px;
        }
      }

      .item2:hover {
        transform: perspective(500px) rotateX(0deg);
      }

      .item3 {
        background-image: linear-gradient(-60deg,
            #1e3c72 0%,
            #1e3c72 1%,
            #2a5298 100%);
        // background-image:linear-gradient(0deg,#434343 0%, #000000 100%)
        // background-image:linear-gradient(0deg,#1e3c72 0%, #1e3c72 1%, #2a5298 100%)
        color: white;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
        transform: perspective(500px) rotateX(5deg);
        transition: transform 0.3s;

        :deep(.el-statistic__content) {
          color: white !important;
          font-size: 64px;
        }
      }

      .item3:hover {
        transform: perspective(500px) rotateX(0deg);
      }

      .item4 {
        background-image: linear-gradient(90deg, #0ba360 0%, #3cba92 100%);
        color: white;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
        transform: perspective(500px) rotateX(5deg);
        transition: transform 0.3s;

        :deep(.el-statistic__content) {
          color: white !important;
          font-size: 64px;
          font-family: Comic Sans MS, cursive, 宋体, sans-serif;
        }
      }

      .item4:hover {
        transform: perspective(500px) rotateX(0deg);
      }
    }

    .chart {
      flex: 1;
      // background-color: #0ba360;
    }

    .excel {
      flex: 1;
      overflow: hidden;
      padding: 10px 0 0 0;
      display: flex;

      .item {
        border-radius: 10px;
        overflow: hidden;
      }

      .item1 {
        flex: 1;
        margin: 0 5px 0 0;
        display: flex;
        flex-direction: column;
        background-color: #30c6ec23;

        // backdrop-filter: blur(10px);
        // -webkit-backdrop-filter: blur(10px);
        :deep(.el-progress__text) {
          font-size: 24px !important;
        }
      }

      .item2 {
        flex: 1;
        background-color: #f096191a;
        margin: 0 5px 0 5px;
        padding: 10px;
      }

      .item3 {
        flex: 2;
        background-color: #30c6ec1d;
        margin: 0 0 0 5px;
        padding: 10px;
      }
    }
  }
}
</style>
