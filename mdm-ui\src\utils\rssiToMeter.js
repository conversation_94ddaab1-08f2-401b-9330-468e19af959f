/**
 * 通过 RSSI 和 AP 高度计算与 AP 的水平距离
 * @param {number} rssi - 接收的 Wi-Fi 信号强度（dBm）
 * @param {number} txPower - 1 米处的信号强度（dBm），默认 -17
 * @param {number} pathLossExponent - 路径损耗因子，默认 3
 * @param {number} height - AP 与设备之间的垂直高度差（米），默认 1.5 米
 * @returns {number} 估算的水平距离（米）
 */
export function rssiToMeter(rssi, txPower = -17, pathLossExponent = 2, height = 2) {
  const ratio = (txPower - rssi) / (10 * pathLossExponent);
  const totalDistance = Math.pow(10, ratio); // 直线距离
  if (totalDistance < height) return 0; // 防止开根号为负数
  const horizontalDistance = Math.sqrt(totalDistance ** 2 - height ** 2);
  return parseFloat(horizontalDistance.toFixed(2)); // 保留2位小数
}
export function handleIphoneLocation(wifiList,apList,ip) {
  console.log(wifiList,apList);
  let arr = []
  for (let i = 0; i < wifiList.length; i++) {
    for  (let j = 0; j < apList.length; j++) { 
      if(wifiList[i].ssid === apList[j][2]) {
        arr.push({
          ssid: wifiList[i].ssid,
          level: wifiList[i].level,
          distance: rssiToMeter(wifiList[i].level, apList[j][3], apList[j][4], apList[j][5]) || 1,
          x: apList[j][0],
          y: apList[j][1],
        });
        continue;
      }
    }
  }
  console.log(arr);
  let iphoneItem = []
  if(arr.length === 1) {
    iphoneItem = [arr[0].x, arr[0].y, ip, arr[0].distance]
  }else if(arr.length === 2) {
    iphoneItem = handleCoordinate2(arr,ip);
  }else if(arr.length === 3) {
    iphoneItem = handleCoordinate3(arr,ip);
  }else if(arr.length >= 4) {
    iphoneItem = multilateration(arr, 1000, 0.01, ip);
  }
  return [iphoneItem];
}

// 2个点
export function handleCoordinate2(arr,ip) {
  const x1 = arr[0].x, y1 = arr[0].y, r1 = arr[0].distance;
  const x2 = arr[1].x, y2 = arr[1].y, r2 = arr[1].distance;
  // 计算向量 长度 单位向量
  const vX = x2 - x1;
  const vY = y2 - y1;
  const vLength = Math.sqrt(vX * vX + vY * vY);
  const uX = vX / vLength, uY = vY / vLength;
  const O1 = {
    x: x1 + uX * r1,
    y: y1 + uY * r1
  }
  const O2 = {
    x: x2 - uX * r2,
    y: y2 - uY * r2
  }
  const x = parseFloat((O1.x + O2.x) / 2).toFixed(2);
  const y = parseFloat((O1.y + O2.y) / 2).toFixed(2);
  let R = parseFloat(Math.sqrt((O1.x - O2.x) ** 2 + (O1.y - O2.y) ** 2) / 2).toFixed(2);
  R < 0.1 ? R = 0.1 : R;
  console.log(R);
  return [x, y, ip, R];
}
// 3个点
function handleCoordinate3(arr,ip) {
  // 通过三点计算圆心和半径
  const [p1, p2, p3] = arr;
  const x1 = p1.x, y1 = p1.y, r1 = p1.distance;
  const x2 = p2.x, y2 = p2.y, r2 = p2.distance;
  const x3 = p3.x, y3 = p3.y, r3 = p3.distance;
  const dx = x2 - x1;
  const dy = y2 - y1;
  const d = Math.hypot(dx, dy);
  if (d === 0) {
    handleCoordinate2([p1, p2], ip);
    return;
  };
  const ex = { x: dx / d, y: dy / d };
  const i = (ex.x * (x3 - x1)) + (ex.y * (y3 - y1));
  const auxx = x3 - x1 - i * ex.x;
  const auxy = y3 - y1 - i * ex.y;
  const j = Math.hypot(auxx, auxy);
  if (j === 0) {
    handleCoordinate2([p1, p2], ip);
    return;
  };
  const ey = { x: auxx / j, y: auxy / j };
  const x = (r1 ** 2 - r2 ** 2 + d ** 2) / (2 * d);
  const y = (r1 ** 2 - r3 ** 2 + i ** 2 + j ** 2 - 2 * i * x) / (2 * j);
  const finalX = x1 + x * ex.x + y * ey.x;
  const finalY = y1 + x * ex.y + y * ey.y;
  return [parseFloat(finalX.toFixed(2)), parseFloat(finalY.toFixed(2)), ip, 0.1];
}
// 4个点
function multilateration(points, maxIter = 1000, learningRate = 0.01, ip) {
  let x = 0, y = 0; // 初始猜测
  for (let iter = 0; iter < maxIter; iter++) {
    let gradX = 0;
    let gradY = 0;
    for (const { x: xi, y: yi, distance: di } of points) {
      const dx = x - xi;
      const dy = y - yi;
      const dist = Math.sqrt(dx * dx + dy * dy) || 1e-6; // 防止除0
      const diff = dist - di;
      gradX += (2 * diff * dx) / dist;
      gradY += (2 * diff * dy) / dist;
    }
    x -= learningRate * gradX;
    y -= learningRate * gradY;
  }
  return [parseFloat(x.toFixed(2)), parseFloat(y.toFixed(2)), ip, 0.1]; // 返回结果，保留2位小数
}