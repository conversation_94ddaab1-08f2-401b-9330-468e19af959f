import { defineConfig, loadEnv } from 'vite'
import path from 'path'
import createVitePlugins from './vite/plugins'

// https://vitejs.dev/config/
export default defineConfig(({ mode, command }) => {
  const env = loadEnv(mode, process.cwd())
  const { VITE_APP_ENV } = env
  return {
    base: VITE_APP_ENV === 'production' ? '/' : '/',
    plugins: createVitePlugins(env, command === 'build'),
    resolve: {
      alias: {
        '~': path.resolve(__dirname, './'),
        '@': path.resolve(__dirname, './src')
      },
      extensions: ['.mjs', '.js', '.ts', '.jsx', '.tsx', '.json', '.vue']
    },
    // vite 相关配置
    server: {
      port: 9002,
      host: true,
      open: true,
      proxy: {
        '/dev-api': {
          // 1 ************ 斯凯奇
          // 1 *********** 维信
          // 1 ************ 公司linux
          target: 'http://***********:5006',
          // target: 'http://***********4:5006',
          // target: 'http://************:5006',
          // target: 'http://1************:5006',
          // target: 'http://************:5006',
          // target: 'http://************:5006',
          // target: 'http://************:9001',
          // target: 'http://************:9001',
          // target: 'http://***********:8081',
          changeOrigin: true,
          rewrite: (p) => p.replace(/^\/dev-api/, '')
        }
      }
    },
    css: {
      postcss: {
        plugins: [
          {
            postcssPlugin: 'internal:charset-removal',
            AtRule: {
              charset: (atRule) => {
                if (atRule.name === 'charset') {
                  atRule.remove();
                }
              }
            }
          }
        ]
      }
    }
  }
})
